import sys
import os
import threading
import time
import json
import datetime
from selenium.webdriver.common.by import By
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QTabWidget, QTableWidget, QTableWidgetItem,
                            QHeaderView, QProgressBar, QMessageBox, QTextEdit, QComboBox,
                            QGroupBox, QFormLayout, QLineEdit, QCheckBox, QSpinBox, QDateEdit,
                            QFileDialog, QListWidget, QListWidgetItem, QProgressDialog, QMenu, QAction, QInputDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette

# Import our service modules
import config
from utils.selenium_utils import initialize_driver, login_to_yahoo
from utils.data_utils import extract_cookies, save_cookies, load_cookies, check_cookie_status, add_cookies_to_driver

# Ensure required directories exist
os.makedirs('data', exist_ok=True)
from services.yahoo_auction_service import YahooAuctionService
from services.tracking_service import TrackingService
from services.invoice_service import InvoiceService

# Import for database connection
from supabase import create_client
import google.generativeai as genai

class WorkerThread(QThread):
    """Worker thread for running background tasks."""
    update_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    progress_signal = pyqtSignal(int)
    invoice_signal = pyqtSignal(dict)
    cost_signal = pyqtSignal(dict)
    cookie_signal = pyqtSignal(dict)

    def __init__(self, task_type, parent=None, params=None):
        super().__init__(parent)
        self.task_type = task_type
        self.params = params or {}
        self.driver = None
        self.supabase = None
        self.genai_client = None
        self.yahoo_service = None
        self.tracking_service = None
        self.invoice_service = None

    def run(self):
        try:
            # Initialize services
            self.update_signal.emit("Initializing services...")
            self.progress_signal.emit(10)

            # Initialize Supabase client
            self.supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)

            # Initialize Gemini client
            genai.configure(api_key=config.GEMINI_API_KEY)
            self.genai_client = genai

            # Initialize WebDriver if needed
            if self.task_type not in ["calculate_cost"]:
                self.update_signal.emit("Initializing WebDriver...")
                self.progress_signal.emit(20)

                # Determine if we should use cookies
                use_cookies = True

                # For these tasks, we need specific cookie behavior
                if self.task_type == "extract_cookies":
                    # Don't use cookies when extracting cookies (we want a fresh session)
                    use_cookies = False
                    self.update_signal.emit("Starting fresh session without cookies for extraction...")
                elif self.task_type == "check_cookies":
                    # For cookie checking, we handle cookies separately
                    use_cookies = False
                    self.update_signal.emit("Starting fresh session for cookie validation...")
                else:
                    # For all other tasks, try to use cookies
                    self.update_signal.emit("Using saved cookies if available...")

                # Initialize the driver with or without cookies
                self.driver = initialize_driver(use_cookies=use_cookies)

                # Login to Yahoo Auctions if needed
                if self.task_type not in ["calculate_cost", "check_cookies"]:
                    self.update_signal.emit("Checking login status...")
                    self.progress_signal.emit(30)

                    # For extract_cookies, we want to force manual login
                    # For other tasks, check if cookies already logged us in
                    check_cookies_first = self.task_type != "extract_cookies"

                    if not login_to_yahoo(self.driver, check_cookies_first=check_cookies_first, auto_login=True):
                        self.update_signal.emit("Login failed. Please check your credentials.")
                        self.finished_signal.emit(False, "Login failed")
                        return
                    else:
                        self.update_signal.emit("Successfully logged in to Yahoo Auctions")

            # Initialize services
            self.update_signal.emit("Initializing services...")
            self.progress_signal.emit(40)

            if self.driver:
                self.yahoo_service = YahooAuctionService(self.driver, self.supabase, self.genai_client)
                self.tracking_service = TrackingService(self.driver, self.supabase)
                self.invoice_service = InvoiceService(self.driver, self.supabase)
            else:
                # For tasks that don't need a driver
                self.invoice_service = InvoiceService(None, self.supabase)

            # Run the requested task
            if self.task_type == "fetch":
                self.fetch_auctions()
            elif self.task_type == "process":
                self.process_auctions()
            elif self.task_type == "feedback":
                self.process_feedback()
            elif self.task_type == "track":
                self.update_tracking()
            elif self.task_type == "all":
                self.run_all_tasks()
            elif self.task_type == "download_invoices":
                self.download_invoices()
            elif self.task_type == "process_invoices":
                self.process_invoices()
            elif self.task_type == "calculate_cost":
                self.calculate_cost()
            elif self.task_type == "extract_cookies":
                self.extract_cookies()
            elif self.task_type == "check_cookies":
                self.check_cookies()

            self.update_signal.emit("Task completed successfully!")
            self.progress_signal.emit(100)
            self.finished_signal.emit(True, "Task completed successfully")

        except Exception as e:
            self.update_signal.emit(f"Error: {str(e)}")
            self.finished_signal.emit(False, str(e))
        finally:
            # Close the WebDriver
            if self.driver:
                self.update_signal.emit("Closing WebDriver...")
                self.driver.quit()

    def fetch_auctions(self):
        """Fetch won auctions."""
        self.update_signal.emit("Fetching won auctions...")
        self.progress_signal.emit(50)
        auctions = self.yahoo_service.fetch_won_auctions()
        self.update_signal.emit(f"Found {len(auctions)} auctions")

        # Add summary information
        if auctions:
            self.update_signal.emit("\nAuction Summary:")
            # Group by status if available
            status_counts = {}
            for auction in auctions:
                status = auction.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            # Log status counts
            for status, count in status_counts.items():
                self.update_signal.emit(f"  - {status}: {count} auctions")

        self.update_signal.emit("Processing auction data...")
        self.progress_signal.emit(75)
        new_auctions = self.yahoo_service.process_auction_data(auctions)
        self.update_signal.emit(f"Saved {len(new_auctions)} new auctions to database")

        # Add more details about new auctions
        if new_auctions:
            self.update_signal.emit("\nNew Auctions:")
            for i, auction in enumerate(new_auctions[:5], 1):  # Show first 5 only
                name = auction.get('name', 'Unknown')
                price = auction.get('price', 0)
                self.update_signal.emit(f"  {i}. {name[:30]}... - ¥{price:,}")

            if len(new_auctions) > 5:
                self.update_signal.emit(f"  ... and {len(new_auctions) - 5} more")

    def process_auctions(self):
        """Process pending auctions."""
        self.update_signal.emit("Processing pending auctions...")
        self.progress_signal.emit(50)
        self.yahoo_service.process_pending_auctions()
        self.progress_signal.emit(90)

    def process_feedback(self):
        """Process feedback."""
        self.update_signal.emit("Processing feedback...")
        self.progress_signal.emit(50)
        self.yahoo_service.process_feedback()
        self.progress_signal.emit(90)

    def update_tracking(self):
        """Update tracking information."""
        self.update_signal.emit("Updating tracking information...")
        self.progress_signal.emit(50)
        self.tracking_service.update_all_tracking()
        self.progress_signal.emit(90)

    def run_all_tasks(self):
        """Run all tasks."""
        self.update_signal.emit("Fetching won auctions...")
        self.progress_signal.emit(20)
        auctions = self.yahoo_service.fetch_won_auctions()
        self.update_signal.emit(f"Found {len(auctions)} auctions")

        self.update_signal.emit("Processing auction data...")
        self.progress_signal.emit(30)
        new_auctions = self.yahoo_service.process_auction_data(auctions)
        self.update_signal.emit(f"Saved {len(new_auctions)} new auctions to database")

        self.update_signal.emit("Processing pending auctions...")
        self.progress_signal.emit(50)
        self.yahoo_service.process_pending_auctions()

        self.update_signal.emit("Processing feedback...")
        self.progress_signal.emit(70)
        self.yahoo_service.process_feedback()

        self.update_signal.emit("Updating tracking information...")
        self.progress_signal.emit(80)
        self.tracking_service.update_all_tracking()

    def download_invoices(self):
        """Download invoices for a plan."""
        plan_id = self.params.get('plan_id', 1)
        self.update_signal.emit(f"Downloading invoices for plan {plan_id}...")
        self.progress_signal.emit(50)

        invoices = self.invoice_service.download_all_invoices(plan_id)
        self.update_signal.emit(f"Downloaded {len(invoices)} invoices")

        # Send invoice data back to UI
        invoice_data = {
            'plan_id': plan_id,
            'invoices': invoices,
            'count': len(invoices)
        }
        self.invoice_signal.emit(invoice_data)

    def process_invoices(self):
        """Process invoices for a plan."""
        plan_id = self.params.get('plan_id', 1)
        self.update_signal.emit(f"Processing invoices for plan {plan_id}...")
        self.progress_signal.emit(50)

        merged_pdf = self.invoice_service.process_invoices(plan_id)
        self.update_signal.emit(f"Processed invoices and saved to {merged_pdf}")

        # Send invoice data back to UI
        invoice_data = {
            'plan_id': plan_id,
            'merged_pdf': merged_pdf
        }
        self.invoice_signal.emit(invoice_data)

    def calculate_cost(self):
        """Calculate total cost for plans."""
        try:
            plan_ids = self.params.get('plan_ids', [1, 2, 5])
            self.update_signal.emit(f"Calculating total cost for plans {plan_ids}...")
            self.progress_signal.emit(20)

            # Get result from invoice service
            result = self.invoice_service.calculate_total_cost(plan_ids)
            self.progress_signal.emit(60)

            # Check for error
            if 'error' in result:
                self.update_signal.emit(f"Error calculating cost: {result['error']}")

                # Send error data back to UI
                cost_data = {
                    'plan_ids': plan_ids,
                    'total_jpy': 0,
                    'total_vnd': 0,
                    'tracking_numbers': [],
                    'auction_count': 0,
                    'expense_count': 0,
                    'exchange_rate': 0,
                    'balance_jpy': 0,
                    'balance_vnd': 0,
                    'error': result['error'],
                    'log_messages': result.get('log_messages', [])
                }
                self.cost_signal.emit(cost_data)
                return

            # Get values from result
            total_jpy = result['total']
            balance_jpy = result.get('balance_jpy', 0)
            balance_vnd = result.get('balance_vnd', 0)
            self.progress_signal.emit(80)

            # Log the result
            self.update_signal.emit(f"Total cost: ¥{total_jpy:,}")

            # Send cost data back to UI
            cost_data = {
                'plan_ids': plan_ids,
                'total_jpy': total_jpy,
                'tracking_numbers': result['tracking_numbers'],
                'auction_count': result['auction_count'],
                'expense_count': result['expense_count'],
                'balance_jpy': balance_jpy,
                'balance_vnd': balance_vnd,
                'log_messages': result.get('log_messages', [])
            }
            self.cost_signal.emit(cost_data)
            self.progress_signal.emit(100)

        except Exception as e:
            import traceback
            error_message = f"Error in calculate_cost: {str(e)}\n{traceback.format_exc()}"
            self.update_signal.emit(error_message)

            # Send error data back to UI
            cost_data = {
                'plan_ids': self.params.get('plan_ids', [1, 2, 5]),
                'total_jpy': 0,
                'tracking_numbers': [],
                'auction_count': 0,
                'expense_count': 0,
                'balance_jpy': 0,
                'balance_vnd': 0,
                'error': str(e),
                'log_messages': [error_message]
            }
            self.cost_signal.emit(cost_data)

    def extract_cookies(self):
        """Extract cookies from the current browser session."""
        self.update_signal.emit("Extracting Yahoo cookies...")
        self.progress_signal.emit(20)

        # Make sure we're logged in to Yahoo
        self.update_signal.emit("Logging in to Yahoo Auctions...")
        self.progress_signal.emit(30)
        if not login_to_yahoo(self.driver, check_cookies_first=False, auto_login=True):
            self.update_signal.emit("Login failed. Cannot extract cookies.")
            self.finished_signal.emit(False, "Login failed")
            return

        self.update_signal.emit("Successfully logged in. Extracting cookies...")
        self.progress_signal.emit(60)

        # Visit multiple Yahoo domains to ensure we get all necessary cookies
        domains = [
            "https://auctions.yahoo.co.jp/",
            "https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won",
            "https://www.yahoo.co.jp/"
        ]

        all_cookies = []
        for domain in domains:
            self.update_signal.emit(f"Visiting {domain}...")
            self.driver.get(domain)
            time.sleep(3)  # Give more time for page to fully load

            # Extract cookies from this domain
            domain_cookies = extract_cookies(self.driver)
            self.update_signal.emit(f"Found {len(domain_cookies)} cookies from {domain}")

            # Add new cookies to our collection
            for cookie in domain_cookies:
                if cookie not in all_cookies:
                    all_cookies.append(cookie)

        # Filter out session cookies that might expire quickly
        important_cookies = [c for c in all_cookies if c.get('domain', '').endswith('yahoo.co.jp')]

        self.update_signal.emit(f"Found total of {len(important_cookies)} Yahoo cookies")

        # Save cookies to file
        filename = self.params.get('filename', 'yahoo_cookies.json')
        if save_cookies(important_cookies, filename):
            self.update_signal.emit(f"Saved cookies to {filename}")

            # Print some cookie details for debugging
            auth_cookies = [c for c in important_cookies if 'auth' in c.get('name', '').lower() or 'login' in c.get('name', '').lower() or 'sess' in c.get('name', '').lower()]
            if auth_cookies:
                self.update_signal.emit(f"Found {len(auth_cookies)} authentication-related cookies")
                for cookie in auth_cookies[:3]:  # Show first 3 auth cookies
                    expiry = cookie.get('expiry', 'unknown')
                    if isinstance(expiry, (int, float)):
                        import datetime
                        expiry_date = datetime.datetime.fromtimestamp(expiry).strftime('%Y-%m-%d %H:%M:%S')
                        self.update_signal.emit(f"Cookie '{cookie['name']}' expires on {expiry_date}")
        else:
            self.update_signal.emit(f"Failed to save cookies to {filename}")

        # Send cookie data back to UI
        cookie_data = {
            'count': len(important_cookies),
            'filename': filename,
            'cookies': important_cookies
        }
        self.cookie_signal.emit(cookie_data)
        self.progress_signal.emit(100)

    def check_cookies(self):
        """Check if cookies are valid."""
        self.update_signal.emit("Checking Yahoo cookies...")
        self.progress_signal.emit(20)

        # Load cookies from file
        filename = self.params.get('filename', 'yahoo_cookies.json')
        cookies = load_cookies(filename)

        if not cookies:
            self.update_signal.emit(f"No cookies found in {filename}")
            cookie_data = {
                'valid': False,
                'message': f"No cookies found in {filename}",
                'count': 0
            }
            self.cookie_signal.emit(cookie_data)
            self.progress_signal.emit(100)
            return

        self.update_signal.emit(f"Loaded {len(cookies)} cookies from {filename}")

        # Method 1: Check using requests library
        self.update_signal.emit("Checking cookies using HTTP request...")
        self.progress_signal.emit(40)
        status = check_cookie_status(cookies)
        self.update_signal.emit(f"HTTP check result: {status['message']}")

        # Method 2: Check using WebDriver (more reliable)
        self.update_signal.emit("Checking cookies using WebDriver in headless mode...")
        self.progress_signal.emit(60)

        # Create a new driver instance with cookies in headless mode
        test_driver = initialize_driver(use_cookies=False, headless=True)  # Start with fresh session in headless mode
        try:
            # Add cookies to the driver
            add_cookies_to_driver(test_driver, cookies)

            # Try to access a page that requires login
            test_driver.get(config.YAHOO_AUCTION_URL)
            time.sleep(3)  # Wait for page to load

            # Check if we're logged in
            login_elements = test_driver.find_elements(By.ID, "login_handle")
            if not login_elements:
                # Check for elements that indicate we're logged in
                username_elements = test_driver.find_elements(By.CLASS_NAME, "LoginMenu__name")
                if username_elements or "ログアウト" in test_driver.page_source:
                    self.update_signal.emit("WebDriver check: Cookies are valid! Successfully logged in.")
                    status['valid'] = True
                    status['message'] = "Cookies are valid and working correctly"
                else:
                    self.update_signal.emit("WebDriver check: Cookies loaded but login status unclear.")
            else:
                self.update_signal.emit("WebDriver check: Cookies are invalid or expired. Login form detected.")
                status['valid'] = False
                status['message'] = "Cookies are invalid or expired"
        except Exception as e:
            self.update_signal.emit(f"Error during WebDriver check: {str(e)}")
        finally:
            # Close the test driver
            test_driver.quit()

        self.progress_signal.emit(80)

        # Send cookie data back to UI
        cookie_data = {
            'valid': status['valid'],
            'message': status['message'],
            'count': len(cookies),
            'filename': filename
        }
        self.cookie_signal.emit(cookie_data)
        self.progress_signal.emit(100)

class AuctionTableWidget(QTableWidget):
    """Custom table widget for displaying auctions."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setColumnCount(11)
        self.setHorizontalHeaderLabels(["ID", "Name", "Price", "Status", "Tracking #", "Tracking Status", "Delivery Method",
                                      "Plan", "Reviewed At", "Scanned At", "Actions"])
        self.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.horizontalHeader().setStretchLastSection(True)
        self.setAlternatingRowColors(True)
        self.verticalHeader().setVisible(False)

        # Connect double-click event
        self.cellDoubleClicked.connect(self.handle_cell_double_clicked)
        self.auctions_data = []

        # Enable context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # Track editing state
        self.editing_cell = None
        self.is_saving = False  # Flag to prevent double saving

        # Connect item changed signal for automatic saving
        self.itemChanged.connect(self.handle_item_changed)

        # Connect delegate signals for better editing detection
        delegate = self.itemDelegate()
        if delegate:
            delegate.commitData.connect(self.handle_commit_data)
            delegate.closeEditor.connect(self.handle_close_editor)

        # Set style
        self.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fff;
            }
            QTableWidget::item:alternate {
                background-color: #f9f9f9;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #000;
            }
            QHeaderView::section {
                background-color: #2196F3;
                color: white;
                padding: 6px;
                border: none;
                font-weight: bold;
            }
        """)

        # Initialize Supabase client for database updates
        self.supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)

    def show_context_menu(self, position):
        """Show context menu for editable cells."""
        item = self.itemAt(position)
        if not item:
            return

        row = item.row()
        column = item.column()

        # Only show context menu for editable columns (Tracking # and Delivery Method)
        if column not in [4, 6]:  # Column 4: Tracking #, Column 6: Delivery Method
            return

        menu = QMenu(self)

        edit_action = QAction("Chỉnh sửa", self)
        edit_action.triggered.connect(lambda: self.start_inline_edit(row, column))
        menu.addAction(edit_action)

        menu.exec_(self.mapToGlobal(position))

    def start_inline_edit(self, row, column):
        """Start inline editing for a cell."""
        self.editing_cell = (row, column)
        item = self.item(row, column)
        if item:
            self.editItem(item)

    def keyPressEvent(self, event):
        """Handle key press events for inline editing."""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.editing_cell:
                self.finish_inline_edit()
                return
        elif event.key() == Qt.Key_Escape:
            if self.editing_cell:
                self.cancel_inline_edit()
                return

        super().keyPressEvent(event)

    def finish_inline_edit(self):
        """Finish inline editing and update database."""
        if not self.editing_cell or self.is_saving:
            return

        self.is_saving = True  # Set flag to prevent double saving

        row, column = self.editing_cell
        item = self.item(row, column)
        if not item:
            self.editing_cell = None
            self.is_saving = False
            return

        new_value = item.text().strip()

        # Get auction ID
        id_item = self.item(row, 0)
        if not id_item:
            self.editing_cell = None
            self.is_saving = False
            return

        auction_id = id_item.text().strip()

        # Determine which field to update
        field_name = ""
        if column == 4:  # Tracking Number
            field_name = "tracking_number"
        elif column == 6:  # Delivery Method
            field_name = "delivery_method"
        else:
            self.editing_cell = None
            self.is_saving = False
            return

        # Update database
        try:
            result = self.supabase.table('auctions').update({field_name: new_value}).eq('id', auction_id).execute()

            # Show success message
            QMessageBox.information(self, "Cập nhật thành công",
                                  f"Đã cập nhật {field_name} thành: {new_value}")

            # Update the local data to reflect the change
            if hasattr(self, 'auctions_data') and row < len(self.auctions_data):
                self.auctions_data[row][field_name] = new_value

            # No need to refresh entire table - the change is already visible

        except Exception as e:
            QMessageBox.warning(self, "Lỗi cập nhật",
                              f"Không thể cập nhật {field_name}: {str(e)}")

            # Restore original value on error
            if hasattr(self, 'auctions_data') and row < len(self.auctions_data):
                original_value = self.auctions_data[row].get(field_name, '')
                item.setText(original_value)

        self.editing_cell = None
        self.is_saving = False

    def handle_item_changed(self, item):
        """Handle when an item is changed (alternative to keyPressEvent)."""
        # Only handle changes for editable columns
        if item.column() not in [4, 6]:  # Only Tracking # and Delivery Method
            return

        # Check if this is the item we're currently editing and not already saving
        if self.editing_cell and self.editing_cell == (item.row(), item.column()) and not self.is_saving:
            self.finish_inline_edit()

    def handle_commit_data(self, editor):
        """Handle when editor commits data."""
        if self.editing_cell and not self.is_saving:
            # Use QTimer to delay the call slightly to ensure the item text is updated
            QTimer.singleShot(10, self.finish_inline_edit)

    def handle_close_editor(self, editor):
        """Handle when editor is closed."""
        # Reset editing state when editor closes
        if self.editing_cell:
            self.editing_cell = None
            self.is_saving = False

    def cancel_inline_edit(self):
        """Cancel inline editing."""
        if self.editing_cell:
            row, column = self.editing_cell
            # Restore original value
            if hasattr(self, 'auctions_data') and row < len(self.auctions_data):
                auction = self.auctions_data[row]
                if column == 4:  # Tracking Number
                    original_value = auction.get('tracking_number', '')
                elif column == 6:  # Delivery Method
                    original_value = auction.get('delivery_method', '')
                else:
                    original_value = ""

                item = self.item(row, column)
                if item:
                    item.setText(original_value)

        self.editing_cell = None

    def load_data(self, auctions):
        """Load auction data into the table."""
        self.setRowCount(0)
        self.auctions_data = auctions  # Store the data for double-click handling
        for row, auction in enumerate(auctions):
            self.insertRow(row)

            # ID
            self.setItem(row, 0, QTableWidgetItem(auction.get('id', '')))

            # Name
            name_item = QTableWidgetItem(auction.get('name', ''))
            name_item.setToolTip(auction.get('name', ''))
            self.setItem(row, 1, name_item)

            # Price
            price = auction.get('price', 0)
            price_item = QTableWidgetItem(f"¥{price:,}" if price else "")
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 2, price_item)

            # Status
            status = auction.get('status', '')
            status_item = QTableWidgetItem(status)
            if status == 'finished':
                status_item.setForeground(QColor('#4CAF50'))  # Green
            elif status == 'processing':
                status_item.setForeground(QColor('#FF9800'))  # Orange
            self.setItem(row, 3, status_item)

            # Tracking Number
            tracking = auction.get('tracking_number', '')
            tracking_item = QTableWidgetItem(tracking if tracking else "")
            # Style tracking number to indicate it's editable and clickable
            tracking_item.setForeground(QColor('#1976D2'))  # Blue color
            font = tracking_item.font()
            font.setUnderline(True)
            tracking_item.setFont(font)
            tracking_item.setToolTip("Double-click để chỉnh sửa hoặc cập nhật trạng thái tracking\nRight-click → Edit để chỉnh sửa")
            # Make it editable
            tracking_item.setFlags(tracking_item.flags() | Qt.ItemIsEditable)
            self.setItem(row, 4, tracking_item)

            # Tracking Status
            tracking_status = auction.get('delivery_status', '')
            tracking_status_item = QTableWidgetItem(tracking_status if tracking_status else "")

            # Set color based on tracking status
            if tracking_status:
                if "配達完了" in tracking_status or "完了" in tracking_status:
                    tracking_status_item.setForeground(QColor('#4CAF50'))  # Green for delivered
                elif "配達中" in tracking_status or "輸送中" in tracking_status:
                    tracking_status_item.setForeground(QColor('#FF9800'))  # Orange for in transit
                elif "受付" in tracking_status:
                    tracking_status_item.setForeground(QColor('#2196F3'))  # Blue for received

            self.setItem(row, 5, tracking_status_item)

            # Delivery Method
            delivery = auction.get('delivery_method', '')
            delivery_item = QTableWidgetItem(delivery if delivery else "")
            # Style delivery method to indicate it's editable
            delivery_item.setForeground(QColor('#4CAF50'))  # Green color
            delivery_item.setToolTip("Double-click để chỉnh sửa phương thức vận chuyển\nRight-click → Edit để chỉnh sửa")
            # Make it editable
            delivery_item.setFlags(delivery_item.flags() | Qt.ItemIsEditable)
            self.setItem(row, 6, delivery_item)

            # Plan ID
            plan_id = auction.get('plan_id', '')
            plan_item = QTableWidgetItem(str(plan_id) if plan_id else "")
            plan_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 7, plan_item)

            # Reviewed At
            reviewed_at = auction.get('reviewed_at', '')
            if reviewed_at:
                # Format the datetime if it's a string
                if isinstance(reviewed_at, str):
                    try:
                        # Try to parse ISO format
                        dt = datetime.datetime.fromisoformat(reviewed_at.replace('Z', '+00:00'))
                        reviewed_at = dt.strftime('%Y-%m-%d %H:%M')
                    except ValueError:
                        # Keep as is if parsing fails
                        pass
            self.setItem(row, 8, QTableWidgetItem(reviewed_at if reviewed_at else ""))

            # Scanned At
            scanned_at = auction.get('scanned_at', '')
            if scanned_at:
                # Format the datetime if it's a string
                if isinstance(scanned_at, str):
                    try:
                        # Try to parse ISO format
                        dt = datetime.datetime.fromisoformat(scanned_at.replace('Z', '+00:00'))
                        scanned_at = dt.strftime('%Y-%m-%d %H:%M')
                    except ValueError:
                        # Keep as is if parsing fails
                        pass
            self.setItem(row, 9, QTableWidgetItem(scanned_at if scanned_at else ""))

            # Actions Button
            # This would be implemented with a custom widget in a real application
            self.setItem(row, 10, QTableWidgetItem("View"))

    def handle_cell_double_clicked(self, row, column):
        """Handle double-click event on a cell."""
        # For tracking number column, show options
        if column == 4:  # Tracking Number column
            tracking_item = self.item(row, column)
            if not tracking_item or not tracking_item.text().strip():
                # If no tracking number, just start editing
                self.start_inline_edit(row, column)
                return

            # If tracking number exists, show options
            options = ["Chỉnh sửa tracking number", "Cập nhật trạng thái tracking"]
            choice, ok = QInputDialog.getItem(self, "Chọn hành động",
                                            "Bạn muốn làm gì với tracking number?",
                                            options, 0, False)
            if ok:
                if choice == options[0]:  # Edit
                    self.start_inline_edit(row, column)
                elif choice == options[1]:  # Update tracking status
                    self.update_tracking_status(row, column)
            return

        # For delivery method column, just start editing
        if column == 6:  # Delivery Method
            self.start_inline_edit(row, column)
            return

    def update_tracking_status(self, row, column):
        """Update tracking status for a tracking number."""
        # Get the tracking number from the cell
        tracking_item = self.item(row, column)
        if not tracking_item or not tracking_item.text().strip():
            QMessageBox.warning(self, "Không có tracking number", "Không tìm thấy tracking number cho đấu giá này.")
            return

        tracking_number = tracking_item.text().strip()

        # Get delivery method from the cell
        delivery_method_item = self.item(row, 6)  # Column 6 has delivery method
        if not delivery_method_item or not delivery_method_item.text().strip():
            QMessageBox.warning(self, "Không có phương thức vận chuyển",
                                "Không tìm thấy phương thức vận chuyển cho đấu giá này. Không thể theo dõi.")
            return

        delivery_method = delivery_method_item.text().strip()

        # Get auction ID from the cell
        id_item = self.item(row, 0)
        if not id_item:
            return

        auction_id = id_item.text().strip()

        # Create a progress dialog
        progress_dialog = QProgressDialog("Đang cập nhật thông tin tracking...", "Hủy bỏ", 0, 100, self.parentWidget())
        progress_dialog.setWindowTitle("Tracking")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(10)

        # Create worker thread for tracking
        main_window = self.parentWidget().parentWidget()  # Get the main window

        # Kiểm tra và khởi tạo tracking_service nếu cần thiết
        if not hasattr(main_window, 'tracking_service') or main_window.tracking_service is None:
            try:
                # Thử khởi tạo service
                import config
                from utils.selenium_utils import initialize_driver
                from services.tracking_service import TrackingService
                from supabase import create_client

                driver = initialize_driver(use_cookies=True)
                supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
                main_window.driver = driver
                main_window.tracking_service = TrackingService(driver, supabase)
                progress_dialog.setValue(15)
            except Exception as e:
                QMessageBox.warning(self, "Không thể khởi tạo service",
                                   f"Không thể khởi tạo tracking service: {str(e)}\n\nVui lòng chạy tác vụ khác trước để khởi tạo service.")
                progress_dialog.cancel()
                return

        # Create and start a worker thread
        class TrackingWorker(QThread):
            update_signal = pyqtSignal(dict)

            def __init__(self, tracking_service, tracking_number, delivery_method):
                super().__init__()
                self.tracking_service = tracking_service
                self.tracking_number = tracking_number
                self.delivery_method = delivery_method

            def run(self):
                result = self.tracking_service.update_single_tracking(self.tracking_number, self.delivery_method)
                self.update_signal.emit(result)

        # Create and start the worker
        worker = TrackingWorker(main_window.tracking_service, tracking_number, delivery_method)

        # Connect signals
        worker.update_signal.connect(lambda result: self.show_tracking_result(result, progress_dialog))
        worker.finished.connect(progress_dialog.close)

        # Connect progress dialog cancel to terminate worker
        progress_dialog.canceled.connect(worker.terminate)

        # Start worker
        worker.start()

        # Show dialog
        progress_dialog.setValue(20)
        progress_dialog.exec_()

    def show_tracking_result(self, result, progress_dialog):
        """Show tracking result."""
        progress_dialog.setValue(90)

        if result['status'] == 'unknown':
            QMessageBox.warning(self, "Không thể cập nhật",
                                f"Không thể cập nhật thông tin tracking:\n{result['details']}")
        else:
            QMessageBox.information(self, "Tracking đã cập nhật",
                                   f"Tracking number: {result['tracking_number']}\n"
                                   f"Dịch vụ: {result['delivery_method']}\n"
                                   f"Trạng thái: {result['status']}")

            # Update the auctions table
            main_window = self.parentWidget().parentWidget()
            main_window.load_auctions()

class MainWindow(QMainWindow):
    """Main application window."""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Yahoo Auction Manager")
        self.setMinimumSize(1000, 600)
        self.worker_thread = None
        self.supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)

        # Khởi tạo Gemini client
        genai.configure(api_key=config.GEMINI_API_KEY)
        self.genai_client = genai

        # Khởi tạo driver và các service
        try:
            self.driver = initialize_driver(use_cookies=True)
            self.yahoo_service = YahooAuctionService(self.driver, self.supabase, self.genai_client)
            self.tracking_service = TrackingService(self.driver, self.supabase)
            self.invoice_service = InvoiceService(self.driver, self.supabase)
            self.statusBar().showMessage("Khởi tạo service thành công")
        except Exception as e:
            self.driver = None
            self.tracking_service = None
            self.yahoo_service = None
            self.invoice_service = InvoiceService(None, self.supabase)
            self.statusBar().showMessage(f"Lỗi khởi tạo WebDriver: {str(e)}")

        # Settings file path
        self.settings_file = 'data/settings.json'
        self.saved_checkbox_states = {}

        # Set application style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border: 1px solid #ddd;
                border-bottom: none;
            }
            QGroupBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                margin-top: 1em;
                padding-top: 10px;
                font-weight: bold;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Create tabs
        self.create_dashboard_tab()
        self.create_auctions_tab()
        self.create_tracking_tab()
        self.create_invoice_tab()
        self.create_cost_tab()
        self.create_settings_tab()

        # Load saved settings
        self.load_settings()

        # Status bar
        self.statusBar().showMessage("Ready")

    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)

        # Title
        title_label = QLabel("Yahoo Auction Manager")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px 0;")
        layout.addWidget(title_label)

        # Actions group
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)

        # Action buttons
        button_layout = QHBoxLayout()

        self.fetch_button = QPushButton("Fetch Auctions")
        self.fetch_button.clicked.connect(lambda: self.run_task("fetch"))
        button_layout.addWidget(self.fetch_button)

        self.process_button = QPushButton("Process Auctions")
        self.process_button.clicked.connect(lambda: self.run_task("process"))
        button_layout.addWidget(self.process_button)

        self.feedback_button = QPushButton("Process Feedback")
        self.feedback_button.clicked.connect(lambda: self.run_task("feedback"))
        button_layout.addWidget(self.feedback_button)

        self.track_button = QPushButton("Update Tracking")
        self.track_button.setIcon(QIcon.fromTheme("network-transmit-receive"))
        self.track_button.setToolTip("Update tracking information for all packages")
        self.track_button.clicked.connect(lambda: self.run_task("track"))
        button_layout.addWidget(self.track_button)

        self.all_button = QPushButton("Run All")
        self.all_button.clicked.connect(lambda: self.run_task("all"))
        self.all_button.setStyleSheet("background-color: #4CAF50;")  # Green
        button_layout.addWidget(self.all_button)

        actions_layout.addLayout(button_layout)

        # Cookie management buttons
        cookie_layout = QHBoxLayout()

        self.extract_cookies_button = QPushButton("Extract Yahoo Cookies")
        self.extract_cookies_button.clicked.connect(lambda: self.run_task("extract_cookies"))
        self.extract_cookies_button.setStyleSheet("background-color: #2196F3;")  # Blue
        cookie_layout.addWidget(self.extract_cookies_button)

        self.check_cookies_button = QPushButton("Check Cookie Status")
        self.check_cookies_button.clicked.connect(lambda: self.run_task("check_cookies"))
        cookie_layout.addWidget(self.check_cookies_button)

        actions_layout.addLayout(cookie_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setAlignment(Qt.AlignCenter)
        actions_layout.addWidget(self.progress_bar)

        # Status log
        log_group = QGroupBox("Status Log")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("background-color: #f9f9f9; border: 1px solid #ddd;")
        log_layout.addWidget(self.log_text)

        # Add groups to layout
        layout.addWidget(actions_group)
        layout.addWidget(log_group)

        # Add tab
        self.tab_widget.addTab(dashboard_widget, "Dashboard")

    def create_auctions_tab(self):
        """Create the auctions tab."""
        auctions_widget = QWidget()
        layout = QVBoxLayout(auctions_widget)

        # Controls
        controls_layout = QHBoxLayout()

        refresh_button = QPushButton("Refresh Data")
        refresh_button.clicked.connect(self.refresh_auction_data)
        controls_layout.addWidget(refresh_button)

        self.status_filter = QComboBox()
        self.status_filter.setObjectName("auction_status_filter")
        self.status_filter.addItems(["All", "Processing", "Finished"])

        # Set saved filter if available
        if hasattr(self, 'saved_checkbox_states') and 'filters' in self.saved_checkbox_states:
            saved_filter = self.saved_checkbox_states.get('filters', {}).get('auction_status')
            if saved_filter in ["All", "Processing", "Finished"]:
                self.status_filter.setCurrentText(saved_filter)

        self.status_filter.currentTextChanged.connect(self.on_filter_changed)
        controls_layout.addWidget(QLabel("Status:"))
        controls_layout.addWidget(self.status_filter)

        # Add plan_id filter
        self.plan_filter = QComboBox()
        self.plan_filter.setObjectName("auction_plan_filter")
        self.plan_filter.addItem("All Plans", None)

        # Load plans from Supabase
        try:
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                self.plan_filter.addItem(f"Plan {plan_id}: {plan_memo}", plan_id)
        except Exception as e:
            # Add a few default plans if we can't load from database
            for i in range(1, 6):
                self.plan_filter.addItem(f"Plan {i}", i)

        # Set saved filter if available
        if hasattr(self, 'saved_checkbox_states') and 'filters' in self.saved_checkbox_states:
            saved_plan = self.saved_checkbox_states.get('filters', {}).get('plan_id')
            if saved_plan is not None:
                # Find the index with this plan_id in the data role
                for i in range(self.plan_filter.count()):
                    if self.plan_filter.itemData(i) == saved_plan:
                        self.plan_filter.setCurrentIndex(i)
                        break

        self.plan_filter.currentIndexChanged.connect(self.on_filter_changed)
        controls_layout.addWidget(QLabel("Plan:"))
        controls_layout.addWidget(self.plan_filter)

        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Add help labels for interaction features
        help_layout = QVBoxLayout()

        tracking_help = QLabel("Double-click vào số tracking để cập nhật trạng thái mới nhất")
        tracking_help.setStyleSheet("color: #1976D2; font-style: italic;")
        help_layout.addWidget(tracking_help)

        edit_help = QLabel("Double-click hoặc right-click → Edit để chỉnh sửa Tracking # và Delivery Method")
        edit_help.setStyleSheet("color: #4CAF50; font-style: italic;")
        help_layout.addWidget(edit_help)

        layout.addLayout(help_layout)

        # Auctions table
        self.auctions_table = AuctionTableWidget()
        layout.addWidget(self.auctions_table)

        # Add tab
        self.tab_widget.addTab(auctions_widget, "Auctions")

        # Load auctions
        self.load_auctions()

    def refresh_auction_data(self):
        """Refresh both plans and auction data."""
        try:
            # Remember the current selection
            current_plan_id = self.plan_filter.currentData()

            # Clear existing items
            self.plan_filter.clear()

            # Add "All Plans" option
            self.plan_filter.addItem("All Plans", None)

            # Load plans from Supabase
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                self.plan_filter.addItem(f"Plan {plan_id}: {plan_memo}", plan_id)

            # Try to restore previous selection
            if current_plan_id is not None:
                for i in range(self.plan_filter.count()):
                    if self.plan_filter.itemData(i) == current_plan_id:
                        self.plan_filter.setCurrentIndex(i)
                        break

            # Add success message to log
            self.log_text.append(f"Refreshed plans: found {len(plans)} plans")

            # Also refresh the auction data
            self.load_auctions()

        except Exception as e:
            # Add error message to log
            self.log_text.append(f"Error refreshing data: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh data: {str(e)}")

    def create_tracking_tab(self):
        """Create the tracking tab."""
        tracking_widget = QWidget()
        layout = QVBoxLayout(tracking_widget)

        # Tracking form
        form_group = QGroupBox("Track Package")
        form_layout = QFormLayout(form_group)

        self.tracking_number_input = QLineEdit()
        form_layout.addRow("Tracking Number:", self.tracking_number_input)

        self.delivery_method_combo = QComboBox()
        self.delivery_method_combo.addItems(["ヤマト運輸", "佐川急便", "西濃運輸", "ゆうパック", "福山通運"])
        form_layout.addRow("Delivery Company:", self.delivery_method_combo)

        track_button = QPushButton("Track")
        track_button.clicked.connect(self.track_single_package)
        form_layout.addRow("", track_button)

        layout.addWidget(form_group)

        # Tracking results
        results_group = QGroupBox("Tracking Results")
        results_layout = QVBoxLayout(results_group)

        self.tracking_results = QTextEdit()
        self.tracking_results.setReadOnly(True)
        results_layout.addWidget(self.tracking_results)

        layout.addWidget(results_group)

        # Add tab
        self.tab_widget.addTab(tracking_widget, "Tracking")

    def track_single_package(self):
        """Track a single package using the input fields."""
        # Get values from input fields
        tracking_number = self.tracking_number_input.text().strip()
        delivery_method = self.delivery_method_combo.currentText()

        if not tracking_number:
            QMessageBox.warning(self, "Tracking Number trống",
                               "Vui lòng nhập tracking number để theo dõi.")
            return

        # Update tracking results display
        self.tracking_results.clear()
        self.tracking_results.append(f"Đang theo dõi {tracking_number} qua {delivery_method}...")

        # Kiểm tra và khởi tạo tracking_service nếu cần thiết
        if not hasattr(self, 'tracking_service') or self.tracking_service is None:
            try:
                # Thử khởi tạo service
                self.tracking_results.append("Đang khởi tạo tracking service...")

                if not hasattr(self, 'driver') or self.driver is None:
                    self.driver = initialize_driver(use_cookies=True)

                self.tracking_service = TrackingService(self.driver, self.supabase)
                self.tracking_results.append("Đã khởi tạo tracking service thành công")
            except Exception as e:
                error_message = f"Không thể khởi tạo tracking service: {str(e)}"
                self.tracking_results.append(error_message)
                QMessageBox.warning(self, "Không thể khởi tạo service",
                                    f"{error_message}\n\nVui lòng chạy tác vụ khác trước để khởi tạo service.")
                return

        # Create progress dialog
        progress_dialog = QProgressDialog("Đang cập nhật thông tin tracking...", "Hủy bỏ", 0, 100, self)
        progress_dialog.setWindowTitle("Tracking")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(10)

        # Create worker thread for tracking
        class TrackingWorker(QThread):
            update_signal = pyqtSignal(dict)

            def __init__(self, tracking_service, tracking_number, delivery_method):
                super().__init__()
                self.tracking_service = tracking_service
                self.tracking_number = tracking_number
                self.delivery_method = delivery_method

            def run(self):
                result = self.tracking_service.update_single_tracking(self.tracking_number, self.delivery_method)
                self.update_signal.emit(result)

        # Create and start the worker
        worker = TrackingWorker(self.tracking_service, tracking_number, delivery_method)

        # Connect signals
        worker.update_signal.connect(lambda result: self.show_tracking_tab_result(result, progress_dialog))
        worker.finished.connect(progress_dialog.close)

        # Connect progress dialog cancel to terminate worker
        progress_dialog.canceled.connect(worker.terminate)

        # Start worker
        worker.start()

        # Show dialog
        progress_dialog.setValue(20)
        progress_dialog.exec_()

    def show_tracking_tab_result(self, result, progress_dialog):
        """Show tracking result in the tracking tab."""
        progress_dialog.setValue(90)

        # Update tracking results display
        self.tracking_results.clear()
        self.tracking_results.append(f"Kết quả theo dõi cho {result['tracking_number']}:")
        self.tracking_results.append(f"Dịch vụ: {result['delivery_method']}")
        self.tracking_results.append(f"Trạng thái: {result['status']}")
        self.tracking_results.append(f"Chi tiết: {result['details']}")

        # Refresh auctions if needed
        if result['status'] != 'unknown':
            self.load_auctions()

    def create_invoice_tab(self):
        """Create the invoice tab."""
        invoice_widget = QWidget()
        layout = QVBoxLayout(invoice_widget)

        # Plan selection
        plan_group = QGroupBox("Plan Selection")
        plan_layout = QFormLayout(plan_group)

        self.plan_combo = QComboBox()

        # Load plans from Supabase
        try:
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                self.plan_combo.addItem(f"Plan {plan_id}: {plan_memo}", plan_id)  # Store plan_id as item data
        except Exception as e:
            # Add default plans as fallback
            self.plan_combo.addItems(["Plan 1", "Plan 2", "Plan 5", "Plan 7"])
            error_label = QLabel(f"Error loading plans: {str(e)}")
            error_label.setStyleSheet("color: red;")
            plan_layout.addRow("", error_label)

        # Add refresh button
        refresh_button = QPushButton("Refresh Data")
        refresh_button.clicked.connect(self.refresh_invoice_data)

        plan_layout.addRow("Select Plan:", self.plan_combo)
        plan_layout.addRow("", refresh_button)

        layout.addWidget(plan_group)

        # Invoice actions
        actions_group = QGroupBox("Invoice Actions")
        actions_layout = QVBoxLayout(actions_group)

        button_layout = QHBoxLayout()

        download_button = QPushButton("Download Invoices")
        download_button.clicked.connect(self.download_invoices)
        button_layout.addWidget(download_button)

        process_button = QPushButton("Process Invoices")
        process_button.clicked.connect(self.process_invoices)
        button_layout.addWidget(process_button)

        open_button = QPushButton("Open Merged PDF")
        open_button.clicked.connect(self.open_merged_pdf)
        button_layout.addWidget(open_button)

        actions_layout.addLayout(button_layout)

        # Progress bar
        self.invoice_progress = QProgressBar()
        self.invoice_progress.setRange(0, 100)
        self.invoice_progress.setValue(0)
        actions_layout.addWidget(self.invoice_progress)

        layout.addWidget(actions_group)

        # Invoice list
        list_group = QGroupBox("Downloaded Invoices")
        list_layout = QVBoxLayout(list_group)

        self.invoice_list = QListWidget()
        list_layout.addWidget(self.invoice_list)

        layout.addWidget(list_group)

        # Status log
        log_group = QGroupBox("Status Log")
        log_layout = QVBoxLayout(log_group)

        self.invoice_log = QTextEdit()
        self.invoice_log.setReadOnly(True)
        log_layout.addWidget(self.invoice_log)

        layout.addWidget(log_group)

        # Add tab
        self.tab_widget.addTab(invoice_widget, "Invoices")

    def create_cost_tab(self):
        """Create the cost calculation tab."""
        cost_widget = QWidget()
        layout = QVBoxLayout(cost_widget)

        # Plan selection
        plan_group = QGroupBox("Plan Selection")
        plan_layout = QVBoxLayout(plan_group)

        # Load plans from Supabase
        self.plan_checkboxes = {}
        try:
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                checkbox = QCheckBox(f"Plan {plan_id}: {plan_memo}")

                # Set object name for saving state
                checkbox.setObjectName(f"plan_checkbox_{plan_id}")

                # Use use_summary from database instead of saved state
                use_summary = plan.get('use_summary', True)
                checkbox.setChecked(use_summary)

                # Connect state change to update database and save settings
                checkbox.stateChanged.connect(lambda state, pid=plan_id: self.update_plan_use_summary(pid, state == 2))
                checkbox.stateChanged.connect(self.save_settings)

                plan_layout.addWidget(checkbox)
                self.plan_checkboxes[plan_id] = checkbox
        except Exception as e:
            error_label = QLabel(f"Error loading plans: {str(e)}")
            error_label.setStyleSheet("color: red;")
            plan_layout.addWidget(error_label)

            # Add default checkboxes as fallback
            self.plan_checkboxes = {}
            for plan_id in [1, 2, 5, 7]:
                checkbox = QCheckBox(f"Plan {plan_id}")

                # Set object name for saving state
                checkbox.setObjectName(f"plan_checkbox_{plan_id}")

                # Default to checked for fallback plans
                checkbox.setChecked(True)

                # Connect state change to update database and save settings
                checkbox.stateChanged.connect(lambda state, pid=plan_id: self.update_plan_use_summary(pid, state == 2))
                checkbox.stateChanged.connect(self.save_settings)

                plan_layout.addWidget(checkbox)
                self.plan_checkboxes[plan_id] = checkbox

        # Add a refresh button
        refresh_button = QPushButton("Refresh Plans")
        refresh_button.clicked.connect(self.refresh_plans)
        plan_layout.addWidget(refresh_button)

        # Add calculate button
        calculate_button = QPushButton("Calculate Total Cost")
        calculate_button.clicked.connect(self.calculate_cost)
        calculate_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        plan_layout.addWidget(calculate_button)

        layout.addWidget(plan_group)

        # Results
        results_group = QGroupBox("Cost Results")
        results_layout = QFormLayout(results_group)

        self.total_jpy_label = QLabel("¥0")
        self.total_jpy_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        results_layout.addRow("Total (JPY):", self.total_jpy_label)

        self.total_vnd_label = QLabel("0 VND")
        self.total_vnd_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        results_layout.addRow("Total (VND):", self.total_vnd_label)

        # Removed exchange rate display

        # Add balance information
        self.balance_jpy_label = QLabel("¥0")
        self.balance_jpy_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #1976D2;")
        results_layout.addRow("Balance JPY:", self.balance_jpy_label)

        self.balance_vnd_label = QLabel("0 VND")
        self.balance_vnd_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #1976D2;")
        results_layout.addRow("Balance VND:", self.balance_vnd_label)

        # Add statistics
        self.auction_count_label = QLabel("0")
        results_layout.addRow("Number of Auctions:", self.auction_count_label)

        self.expense_count_label = QLabel("0")
        results_layout.addRow("Number of Additional Expenses:", self.expense_count_label)

        self.tracking_count_label = QLabel("0")
        results_layout.addRow("Number of Unique Tracking Numbers:", self.tracking_count_label)

        layout.addWidget(results_group)

        # Status log
        log_group = QGroupBox("Status Log")
        log_layout = QVBoxLayout(log_group)

        self.cost_log = QTextEdit()
        self.cost_log.setReadOnly(True)
        log_layout.addWidget(self.cost_log)

        layout.addWidget(log_group)

        # Add tab
        self.tab_widget.addTab(cost_widget, "Cost Calculation")

    def create_settings_tab(self):
        """Create the settings tab."""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # API Settings
        api_group = QGroupBox("API Settings")
        api_layout = QFormLayout(api_group)

        supabase_url = QLineEdit(config.SUPABASE_URL)
        api_layout.addRow("Supabase URL:", supabase_url)

        supabase_key = QLineEdit(config.SUPABASE_KEY)
        supabase_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Supabase Key:", supabase_key)

        gemini_key = QLineEdit(config.GEMINI_API_KEY)
        gemini_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Gemini API Key:", gemini_key)

        layout.addWidget(api_group)

        # Yahoo Settings
        yahoo_group = QGroupBox("Yahoo Auction Settings")
        yahoo_layout = QFormLayout(yahoo_group)

        yahoo_username = QLineEdit(config.YAHOO_USERNAME)
        yahoo_layout.addRow("Yahoo Username:", yahoo_username)

        yahoo_password = QLineEdit(config.YAHOO_PASSWORD)
        yahoo_password.setEchoMode(QLineEdit.Password)
        yahoo_layout.addRow("Yahoo Password:", yahoo_password)

        layout.addWidget(yahoo_group)

        # Selenium Settings
        selenium_group = QGroupBox("Selenium Settings")
        selenium_layout = QFormLayout(selenium_group)

        incognito = QCheckBox("Incognito Mode")
        incognito.setChecked(config.CHROME_OPTIONS.get("incognito", True))
        selenium_layout.addRow("", incognito)

        no_sandbox = QCheckBox("No Sandbox")
        no_sandbox.setChecked(config.CHROME_OPTIONS.get("no_sandbox", True))
        selenium_layout.addRow("", no_sandbox)

        layout.addWidget(selenium_group)

        # Save button
        save_button = QPushButton("Save Settings")
        layout.addWidget(save_button)

        layout.addStretch()

        # Add tab
        self.tab_widget.addTab(settings_widget, "Settings")

    def on_filter_changed(self, text):
        """Handle filter change and save settings."""
        # Save the filter setting
        self.save_settings()

        # Load auctions with new filter
        self.load_auctions()

    def load_auctions(self):
        """Load auctions from the database."""
        try:
            # Get status filter
            status_filter = self.status_filter.currentText() if hasattr(self, 'status_filter') else "All"

            # Get plan filter
            plan_id = None
            if hasattr(self, 'plan_filter'):
                plan_id = self.plan_filter.currentData()

            # Query auctions with all fields - force fresh data
            query = self.supabase.from_('auctions').select('*,reviewed_at,scanned_at,plan_id')

            # Apply status filter
            if status_filter == "Processing":
                query = query.eq('status', 'processing')
            elif status_filter == "Finished":
                query = query.eq('status', 'finished')

            # Apply plan filter
            if plan_id is not None:
                query = query.eq('plan_id', plan_id)

            # Order by auction end time
            query = query.order('auction_end_time', desc=True)

            # Execute query with count to force fresh data
            result = query.execute(count='exact')

            # Load data into table
            self.auctions_table.load_data(result.data)

            # Update status bar
            filter_description = f"status: {status_filter}"
            if plan_id is not None:
                filter_description += f", plan: {plan_id}"

            self.statusBar().showMessage(f"Loaded {len(result.data)} auctions with {filter_description}")

            # Add summary to log
            self.log_text.append(f"Loaded {len(result.data)} auctions with {filter_description}")

            # Count by status
            status_counts = {}
            for auction in result.data:
                status = auction.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            # Log status counts
            for status, count in status_counts.items():
                self.log_text.append(f"  - {status}: {count} auctions")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load auctions: {str(e)}")

    def run_task(self, task_type, params=None):
        """Run a task in a background thread."""
        # Disable buttons
        self.fetch_button.setEnabled(False)
        self.process_button.setEnabled(False)
        self.feedback_button.setEnabled(False)
        self.track_button.setEnabled(False)
        self.all_button.setEnabled(False)
        self.extract_cookies_button.setEnabled(False)
        self.check_cookies_button.setEnabled(False)

        # Reset progress bar
        self.progress_bar.setValue(0)

        # Clear log
        self.log_text.clear()

        # Create and start worker thread
        self.worker_thread = WorkerThread(task_type, params=params)
        self.worker_thread.update_signal.connect(self.update_log)
        self.worker_thread.finished_signal.connect(self.task_finished)
        self.worker_thread.progress_signal.connect(self.progress_bar.setValue)
        self.worker_thread.invoice_signal.connect(self.handle_invoice_result)
        self.worker_thread.cost_signal.connect(self.handle_cost_result)
        self.worker_thread.cookie_signal.connect(self.handle_cookie_result)
        self.worker_thread.start()

    def update_log(self, message):
        """Update the log with a message."""
        # Add timestamp to message
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"

        self.log_text.append(formatted_message)
        # Scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def task_finished(self, success, message):
        """Handle task completion."""
        # Enable buttons
        self.fetch_button.setEnabled(True)
        self.process_button.setEnabled(True)
        self.feedback_button.setEnabled(True)
        self.track_button.setEnabled(True)
        self.all_button.setEnabled(True)
        self.extract_cookies_button.setEnabled(True)
        self.check_cookies_button.setEnabled(True)

        # Show message
        if success:
            QMessageBox.information(self, "Success", message)
            # Refresh auctions
            self.load_auctions()
        else:
            QMessageBox.critical(self, "Error", message)

    def refresh_invoice_data(self):
        """Refresh the invoice data including plans and other related information."""
        try:
            # Clear existing items
            self.plan_combo.clear()

            # Load plans from Supabase
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                self.plan_combo.addItem(f"Plan {plan_id}: {plan_memo}", plan_id)  # Store plan_id as item data

            # Clear invoice list to reflect the updated plans
            self.invoice_list.clear()

            # Add success message to log
            self.invoice_log.append(f"Refreshed data: found {len(plans)} plans")

        except Exception as e:
            # Add default plans as fallback
            self.plan_combo.addItems(["Plan 1", "Plan 2", "Plan 5", "Plan 7"])

            # Add error message to log
            self.invoice_log.append(f"Error refreshing data: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh data: {str(e)}")

    def download_invoices(self):
        """Download invoices for the selected plan."""
        # Get selected plan
        plan_text = self.plan_combo.currentText()

        # Extract plan ID from text or use item data
        try:
            # Try to get plan_id from item data first
            plan_id = self.plan_combo.currentData()
            if plan_id is None:
                # Fall back to parsing from text
                plan_id = int(plan_text.split(" ")[1].split(":")[0])
        except Exception:
            # If all else fails, just use the first number found
            import re
            numbers = re.findall(r'\d+', plan_text)
            if numbers:
                plan_id = int(numbers[0])
            else:
                QMessageBox.warning(self, "Invalid Plan", "Could not determine plan ID. Please select a valid plan.")
                return

        # Clear invoice list
        self.invoice_list.clear()

        # Clear log
        self.invoice_log.clear()
        self.invoice_log.append(f"Downloading invoices for Plan {plan_id}...")

        # Reset progress bar
        self.invoice_progress.setValue(0)

        # Create and start worker thread
        self.worker_thread = WorkerThread("download_invoices", params={"plan_id": plan_id})
        self.worker_thread.update_signal.connect(self.update_invoice_log)
        self.worker_thread.finished_signal.connect(self.invoice_task_finished)
        self.worker_thread.progress_signal.connect(self.invoice_progress.setValue)
        self.worker_thread.invoice_signal.connect(self.handle_invoice_result)
        self.worker_thread.start()

    def process_invoices(self):
        """Process invoices for the selected plan."""
        # Get selected plan
        plan_text = self.plan_combo.currentText()

        # Extract plan ID from text or use item data
        try:
            # Try to get plan_id from item data first
            plan_id = self.plan_combo.currentData()
            if plan_id is None:
                # Fall back to parsing from text
                plan_id = int(plan_text.split(" ")[1].split(":")[0])
        except Exception:
            # If all else fails, just use the first number found
            import re
            numbers = re.findall(r'\d+', plan_text)
            if numbers:
                plan_id = int(numbers[0])
            else:
                QMessageBox.warning(self, "Invalid Plan", "Could not determine plan ID. Please select a valid plan.")
                return

        # Clear log
        self.invoice_log.clear()
        self.invoice_log.append(f"Processing invoices for Plan {plan_id}...")

        # Reset progress bar
        self.invoice_progress.setValue(0)

        # Create and start worker thread
        self.worker_thread = WorkerThread("process_invoices", params={"plan_id": plan_id})
        self.worker_thread.update_signal.connect(self.update_invoice_log)
        self.worker_thread.finished_signal.connect(self.invoice_task_finished)
        self.worker_thread.progress_signal.connect(self.invoice_progress.setValue)
        self.worker_thread.invoice_signal.connect(self.handle_invoice_result)
        self.worker_thread.start()

    def open_merged_pdf(self):
        """Open the merged PDF file."""
        # Get selected plan
        plan_text = self.plan_combo.currentText()

        # Extract plan ID from text or use item data
        try:
            # Try to get plan_id from item data first
            plan_id = self.plan_combo.currentData()
            if plan_id is None:
                # Fall back to parsing from text
                plan_id = int(plan_text.split(" ")[1].split(":")[0])
        except Exception:
            # If all else fails, just use the first number found
            import re
            numbers = re.findall(r'\d+', plan_text)
            if numbers:
                plan_id = int(numbers[0])
            else:
                QMessageBox.warning(self, "Invalid Plan", "Could not determine plan ID. Please select a valid plan.")
                return

        # Check if file exists
        pdf_path = f"invoices/{plan_id}/Invoices_{plan_id}.pdf"
        if os.path.exists(pdf_path):
            # Open PDF with default application
            if sys.platform == "darwin":  # macOS
                os.system(f"open {pdf_path}")
            elif sys.platform == "win32":  # Windows
                os.system(f"start {pdf_path}")
            else:  # Linux
                os.system(f"xdg-open {pdf_path}")

            # Log the action
            self.invoice_log.append(f"Opened PDF file: {pdf_path}")
        else:
            error_message = f"The file {pdf_path} does not exist. Please process invoices first."
            self.invoice_log.append(f"Error: {error_message}")
            QMessageBox.warning(self, "File Not Found", error_message)

    def update_invoice_log(self, message):
        """Update the invoice log with a message."""
        self.invoice_log.append(message)
        # Scroll to bottom
        self.invoice_log.verticalScrollBar().setValue(self.invoice_log.verticalScrollBar().maximum())

    def invoice_task_finished(self, success, message):
        """Handle invoice task completion."""
        # Enable buttons (if there are any to enable)
        if hasattr(self, 'download_button'):
            self.download_button.setEnabled(True)
        if hasattr(self, 'process_button'):
            self.process_button.setEnabled(True)
        if hasattr(self, 'open_button'):
            self.open_button.setEnabled(True)

        # Show message
        if success:
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)

    def handle_invoice_result(self, data):
        """Handle invoice result data."""
        if "invoices" in data:
            # Clear list
            self.invoice_list.clear()

            # Add invoices to list
            for invoice in data["invoices"]:
                item = QListWidgetItem(os.path.basename(invoice))
                item.setData(Qt.UserRole, invoice)  # Store full path
                self.invoice_list.addItem(item)

    def refresh_plans(self):
        """Refresh the list of plans from Supabase."""
        try:
            # Clear existing checkboxes
            for checkbox in self.plan_checkboxes.values():
                checkbox.setParent(None)  # Remove from layout
            self.plan_checkboxes.clear()

            # Get plan group layout
            plan_group = self.sender().parent()
            plan_layout = plan_group.layout()

            # Remove error label if it exists
            for i in range(plan_layout.count()):
                item = plan_layout.itemAt(i)
                if item and item.widget() and isinstance(item.widget(), QLabel):
                    item.widget().setParent(None)

            # Load plans from Supabase
            plans = self.supabase.from_('plans').select('*').execute().data
            for plan in plans:
                plan_id = plan['id']
                plan_memo = plan.get('memo', f'Plan {plan_id}')
                checkbox = QCheckBox(f"Plan {plan_id}: {plan_memo}")

                # Use use_summary from database
                use_summary = plan.get('use_summary', True)
                checkbox.setChecked(use_summary)

                # Connect state change to update database and save settings
                checkbox.stateChanged.connect(lambda state, pid=plan_id: self.update_plan_use_summary(pid, state == 2))
                checkbox.stateChanged.connect(self.save_settings)

                plan_layout.insertWidget(plan_layout.count() - 2, checkbox)  # Insert before buttons
                self.plan_checkboxes[plan_id] = checkbox

            # Add success message to log
            self.cost_log.append(f"Refreshed plans: found {len(plans)} plans")

        except Exception as e:
            # Add error message to log
            self.cost_log.append(f"Error refreshing plans: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh plans: {str(e)}")

    def calculate_cost(self):
        """Calculate total cost for selected plans."""
        # Get selected plans
        plan_ids = []
        for plan_id, checkbox in self.plan_checkboxes.items():
            if checkbox.isChecked():
                plan_ids.append(plan_id)

        if not plan_ids:
            QMessageBox.warning(self, "No Plans Selected", "Please select at least one plan.")
            return

        # Clear log
        self.cost_log.clear()
        self.cost_log.append(f"Calculating total cost for plans: {', '.join(map(str, plan_ids))}")

        try:
            # Create and start worker thread
            self.worker_thread = WorkerThread("calculate_cost", params={"plan_ids": plan_ids})
            self.worker_thread.update_signal.connect(self.update_cost_log)
            self.worker_thread.finished_signal.connect(self.cost_task_finished)
            self.worker_thread.cost_signal.connect(self.handle_cost_result)
            self.worker_thread.start()
        except Exception as e:
            self.cost_log.append(f"Error starting calculation: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start calculation: {str(e)}")

    def update_cost_log(self, message):
        """Update the cost log with a message."""
        self.cost_log.append(message)
        # Scroll to bottom
        self.cost_log.verticalScrollBar().setValue(self.cost_log.verticalScrollBar().maximum())

    def cost_task_finished(self, success, message):
        """Handle cost task completion."""
        # Show message
        if success:
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)

    def handle_cost_result(self, data):
        """Handle cost result data."""
        try:
            # Check for error
            if 'error' in data:
                self.cost_log.append(f"\nERROR: {data['error']}")
                QMessageBox.critical(self, "Error", f"Error calculating cost: {data['error']}")
                return

            # Update labels
            self.total_jpy_label.setText(f"¥{data['total_jpy']:,}")

            # Calculate VND amount using fixed rate (for display only)
            total_vnd = data['total_jpy'] * 170  # Fixed rate for display
            self.total_vnd_label.setText(f"{total_vnd:,.0f} VND")
            self.auction_count_label.setText(str(data['auction_count']))
            self.expense_count_label.setText(str(data['expense_count']))

            # Update balance information
            if 'balance_jpy' in data:
                balance_jpy = data['balance_jpy']
                # Format balance with Japanese style (万円)
                # Calculate man (10,000s) and remainder
                man = balance_jpy // 10000
                remainder = balance_jpy % 10000

                # For negative numbers with remainder, adjust the display
                if balance_jpy < 0 and remainder != 0:
                    # Convert to positive representation
                    abs_man = abs(man) - 1  # Reduce absolute value of man by 1
                    abs_remainder = 10000 - abs(remainder)  # Calculate positive remainder

                    # Format with negative sign
                    if abs_man == 0:
                        # If man part becomes 0, just show the remainder with negative sign
                        balance_text = f"-{abs_remainder}円"
                    else:
                        balance_text = f"Số dư:  -{abs_man} 万 {abs_remainder}円"
                else:
                    # For positive numbers or negative without remainder
                    if remainder == 0:
                        balance_text = f"{man}万"
                    else:
                        balance_text = f"Số dư:  {man} 万 {remainder}円"

                self.balance_jpy_label.setText(f"¥{balance_jpy:,} ({balance_text})")

                # Set color based on balance (red for negative, green for positive)
                if balance_jpy < 0:
                    self.balance_jpy_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #F44336;")
                else:
                    self.balance_jpy_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #4CAF50;")

            if 'balance_vnd' in data:
                balance_vnd = data['balance_vnd']
                self.balance_vnd_label.setText(f"{balance_vnd:,.0f} VND")

            # Update tracking count
            tracking_count = len(data.get('tracking_numbers', []))
            self.tracking_count_label.setText(str(tracking_count))

            # Display log messages if available
            if 'log_messages' in data and data['log_messages']:
                for message in data['log_messages']:
                    self.cost_log.append(message)
            else:
                # Add detailed information to log if no log messages
                self.cost_log.append(f"\nDetailed Results:")
                self.cost_log.append(f"Total (JPY): ¥{data['total_jpy']:,}")

                # Calculate VND amount using fixed rate (for display only)
                total_vnd = data['total_jpy'] * 170  # Fixed rate for display
                self.cost_log.append(f"Total (VND): {total_vnd:,.0f} VND (estimated)")

                if 'balance_jpy' in data:
                    self.cost_log.append(f"Balance JPY: ¥{data['balance_jpy']:,}")
                if 'balance_vnd' in data:
                    self.cost_log.append(f"Balance VND: {data['balance_vnd']:,.0f} VND")

                self.cost_log.append(f"Number of Auctions: {data['auction_count']}")
                self.cost_log.append(f"Number of Additional Expenses: {data['expense_count']}")
                self.cost_log.append(f"Number of Unique Tracking Numbers: {tracking_count}")

                # List tracking numbers if available
                if tracking_count > 0:
                    self.cost_log.append(f"\nTracking Numbers:")
                    for i, tracking in enumerate(data['tracking_numbers'], 1):
                        self.cost_log.append(f"{i}. {tracking}")

            # Flash the total amount labels to draw attention
            original_style = self.total_jpy_label.styleSheet()
            self.total_jpy_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #4CAF50;")
            self.total_vnd_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #4CAF50;")

            # Reset style after 1 second using QTimer
            QTimer.singleShot(1000, lambda: self.total_jpy_label.setStyleSheet(original_style))
            QTimer.singleShot(1000, lambda: self.total_vnd_label.setStyleSheet(original_style))

        except Exception as e:
            import traceback
            error_message = f"Error processing cost result: {str(e)}\n{traceback.format_exc()}"
            self.cost_log.append(error_message)
            QMessageBox.critical(self, "Error", f"Error processing cost result: {str(e)}")

    def handle_cookie_result(self, cookie_data):
        """Handle cookie extraction or check result."""
        # Show message box with result
        if 'valid' in cookie_data:
            # This is a cookie check result
            if cookie_data['valid']:
                QMessageBox.information(self, "Cookie Status",
                                      f"Cookies are valid! Found {cookie_data['count']} cookies in {cookie_data['filename']}")
            else:
                QMessageBox.warning(self, "Cookie Status",
                                  f"Cookies are invalid or expired. {cookie_data['message']}")
        else:
            # This is a cookie extraction result
            QMessageBox.information(self, "Cookie Extraction",
                                  f"Successfully extracted {cookie_data['count']} cookies to {cookie_data['filename']}")

    def update_plan_use_summary(self, plan_id, use_summary):
        """Update use_summary field in database when checkbox changes."""
        try:
            # Update use_summary in database
            result = self.supabase.from_('plans').update({'use_summary': use_summary}).eq('id', plan_id).execute()
            print(f"Updated plan {plan_id} use_summary to {use_summary}")

            # Add log message to cost log if it exists
            if hasattr(self, 'cost_log'):
                self.cost_log.append(f"Updated Plan {plan_id} use_summary to {use_summary}")

        except Exception as e:
            print(f"Error updating plan {plan_id} use_summary: {e}")
            # Show error message to user
            QMessageBox.warning(self, "Database Error", f"Failed to update plan {plan_id}: {str(e)}")

    def save_settings(self):
        """Save application settings to file."""
        try:
            settings = {
                'checkboxes': {},
                'filters': {}
            }

            # Save auction status filter
            if hasattr(self, 'status_filter'):
                settings['filters']['auction_status'] = self.status_filter.currentText()

            # Save plan filter
            if hasattr(self, 'plan_filter'):
                settings['filters']['plan_id'] = self.plan_filter.currentData()

            # Save checkbox states
            # Cost tab plan checkboxes
            for plan_id, checkbox in self.plan_checkboxes.items():
                settings['checkboxes'][f'plan_{plan_id}'] = checkbox.isChecked()

            # Selenium settings checkboxes
            for child in self.findChildren(QCheckBox):
                if child.objectName():
                    settings['checkboxes'][child.objectName()] = child.isChecked()

            # Save to file
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)

            print(f"Settings saved to {self.settings_file}")

        except Exception as e:
            print(f"Error saving settings: {e}")

    def load_settings(self):
        """Load application settings from file."""
        try:
            if not os.path.exists(self.settings_file):
                print(f"Settings file {self.settings_file} not found, using defaults")
                return

            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # Load checkbox states
            checkboxes = settings.get('checkboxes', {})

            # Apply to plan checkboxes when they're created
            self.saved_checkbox_states = checkboxes

            print(f"Settings loaded from {self.settings_file}")

        except Exception as e:
            print(f"Error loading settings: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        try:
            # Đóng driver nếu đang tồn tại
            if hasattr(self, 'driver') and self.driver is not None:
                self.driver.quit()
                self.driver = None
                print("Closed WebDriver")
        except Exception as e:
            print(f"Error closing WebDriver: {str(e)}")

        # Lưu settings
        self.save_settings()

        # Tiếp tục sự kiện đóng
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
