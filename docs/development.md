# Hướng Dẫn Phát Triển

## Quy Ước Đặt Tên

### Python

- **Tên biến và hàm**: snake_case (ví dụ: `auction_status`, `get_tracking_number()`)
- **Tên lớp**: PascalCase (ví dụ: `YahooAuctionService`, `TrackingService`)
- **Hằng số**: UPPER_SNAKE_CASE (ví dụ: `MAX_RETRIES`, `API_URL`)
- **File module**: snake_case (ví dụ: `yahoo_auction_service.py`, `data_utils.py`)

### Cơ Sở Dữ Liệu

- **Tên bảng**: snake_case, số nhiều (ví dụ: `auctions`, `additional_expenses`)
- **Trường**: snake_case (ví dụ: `auction_id`, `shipping_fee`)
- **Khóa chính**: `id` (UUID)
- **Khóa ngoại**: `<tên_bảng_số_ít>_id` (ví dụ: `auction_id`)

## Tiêu <PERSON> Code

### Import

```python
# Thư viện tiêu chuẩn
import os
import time
import json

# Thư viện bên thứ ba
import selenium
from selenium import webdriver
from supabase import create_client

# Module nội bộ
from utils.data_utils import parse_html
from services.tracking_service import TrackingService
```

### Docstring

```python
def fetch_tracking_info(tracking_number, service_name):
    """
    Lấy thông tin theo dõi từ dịch vụ vận chuyển.
    
    Args:
        tracking_number (str): Mã theo dõi gói hàng
        service_name (str): Tên dịch vụ vận chuyển
        
    Returns:
        dict: Thông tin trạng thái vận chuyển hoặc None nếu không tìm thấy
        
    Raises:
        ValueError: Nếu mã theo dõi không hợp lệ
        ConnectionError: Nếu không thể kết nối đến dịch vụ
    """
    # Mã triển khai
```

### Xử Lý Lỗi

```python
try:
    result = service.get_tracking(tracking_number)
    return result
except ConnectionError as e:
    logger.error(f"Lỗi kết nối: {str(e)}")
    return None
except Exception as e:
    logger.exception(f"Lỗi không xác định: {str(e)}")
    raise
```

### Logging

```python
import logging

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/app.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Sử dụng
logger.info("Bắt đầu lấy dữ liệu đấu giá")
logger.error("Không thể kết nối đến Yahoo Auction")
logger.debug("Dữ liệu phản hồi: %s", response_data)
```

## Mẫu Code

### Khởi Tạo Chrome Driver

```python
def initialize_driver():
    """Khởi tạo và trả về một instance của Chrome WebDriver."""
    options = webdriver.ChromeOptions()
    
    if config.CHROME_OPTIONS.get("incognito"):
        options.add_argument("--incognito")
    
    if config.CHROME_OPTIONS.get("no_sandbox"):
        options.add_argument("--no-sandbox")
    
    if config.CHROME_OPTIONS.get("disable_dev_shm_usage"):
        options.add_argument("--disable-dev-shm-usage")
    
    options.add_argument("--window-size=1920,1080")
    
    driver = webdriver.Chrome(options=options)
    driver.set_page_load_timeout(30)
    
    return driver
```

### Kết Nối Supabase

```python
from supabase import create_client

def connect_to_supabase():
    """Kết nối đến Supabase và trả về client."""
    supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
    return supabase

# Sử dụng
supabase = connect_to_supabase()
```

### Xử Lý Hóa Đơn

```python
from services.invoice_service import InvoiceService

# Khởi tạo dịch vụ hóa đơn
invoice_service = InvoiceService(driver, supabase)

# Tải hóa đơn cho một đấu giá cụ thể
invoice_path = invoice_service.download_invoice(
    auction_id="a123456789",
    contact_url="https://contact.auctions.yahoo.co.jp/...",
    plan_id=1
)

# Tải tất cả hóa đơn cho một kế hoạch
invoice_paths = invoice_service.download_all_invoices(plan_id=1)

# Xử lý và gộp hóa đơn
merged_invoice_path = invoice_service.process_invoices(plan_id=1)

# Tính toán tổng chi phí
cost_details = invoice_service.calculate_total_cost(plan_ids=[1, 2])
print(f"Tổng chi phí: ¥{cost_details['total']:,}")
print(f"Số dư JPY: ¥{cost_details['balance_jpy']:,}")
```

### Gửi HTTP Request

```python
import httpx
import json
from typing import Dict, Any, Optional

async def make_api_request(
    url: str,
    method: str = "GET",
    params: Optional[Dict[str, Any]] = None,
    data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30
) -> Dict[str, Any]:
    """
    Thực hiện yêu cầu API bất đồng bộ.
    
    Args:
        url: URL đích
        method: Phương thức HTTP (GET, POST, v.v.)
        params: Tham số query string
        data: Dữ liệu body (cho POST, PUT)
        headers: Headers HTTP
        timeout: Thời gian chờ tối đa (giây)
        
    Returns:
        Dict[str, Any]: Dữ liệu phản hồi
        
    Raises:
        httpx.HTTPError: Nếu yêu cầu thất bại
    """
    async with httpx.AsyncClient() as client:
        response = await client.request(
            method=method,
            url=url,
            params=params,
            json=data,
            headers=headers,
            timeout=timeout
        )
        response.raise_for_status()
        return response.json()
```

### Truy Vấn Supabase

```python
def get_pending_auctions(supabase):
    """Truy vấn các đấu giá đang chờ xử lý."""
    response = supabase.table("auctions") \
        .select("*") \
        .eq("status", "PENDING") \
        .execute()
    
    if len(response.data) == 0:
        logger.info("Không có đấu giá nào đang chờ xử lý")
        return []
    
    logger.info(f"Tìm thấy {len(response.data)} đấu giá đang chờ xử lý")
    return response.data

def update_auction_status(supabase, auction_id, new_status, tracking_data=None):
    """Cập nhật trạng thái của đấu giá."""
    data = {"status": new_status, "updated_at": "now()"}
    
    if tracking_data:
        data.update(tracking_data)
    
    response = supabase.table("auctions") \
        .update(data) \
        .eq("id", auction_id) \
        .execute()
    
    return response.data
```

### Phân Tích HTML

```python
from bs4 import BeautifulSoup

def parse_auction_details(html_content):
    """Phân tích HTML để trích xuất chi tiết đấu giá."""
    soup = BeautifulSoup(html_content, "html.parser")
    
    # Lấy tiêu đề
    title_element = soup.select_one("h1.ProductTitle__text")
    title = title_element.text.strip() if title_element else "Không có tiêu đề"
    
    # Lấy giá
    price_element = soup.select_one("span.Price__value")
    price_text = price_element.text.strip() if price_element else "0"
    price = int(price_text.replace(",", ""))
    
    # Lấy người bán
    seller_element = soup.select_one("a.SellerInfo__name")
    seller = seller_element.text.strip() if seller_element else "Không xác định"
    
    return {
        "title": title,
        "price": price,
        "seller": seller
    }
```

## Quy Trình Phát Triển

### 1. Cài Đặt Môi Trường

```bash
# Tạo và kích hoạt môi trường ảo
python -m venv venv
source venv/bin/activate  # trên Linux/Mac
venv\Scripts\activate     # trên Windows

# Cài đặt phụ thuộc
pnpm install
```

### 2. Phát Triển Tính Năng Mới

1. **Tạo nhánh mới**:
   ```bash
   git checkout -b feature/new-feature-name
   ```

2. **Viết code và test**:
   - Tuân thủ quy ước đặt tên
   - Thêm docstrings cho mọi hàm và lớp
   - Thêm logging ở các điểm quan trọng
   - Xử lý lỗi một cách thích hợp

3. **Commit và push nhánh**:
   ```bash
   git add .
   git commit -m "feat: thêm tính năng XYZ"
   git push origin feature/new-feature-name
   ```

4. **Tạo Pull Request**

### 3. Môi Trường Biến

Tạo file `.env` với các biến cần thiết. Sử dụng `.env.example` làm mẫu.

### 4. Logging và Debug

- Sử dụng cấp độ logging phù hợp:
  - `DEBUG`: Thông tin chi tiết dành cho debug
  - `INFO`: Thông tin về quy trình chung
  - `WARNING`: Vấn đề tiềm ẩn nhưng không ảnh hưởng đến hoạt động
  - `ERROR`: Lỗi ngăn cản hoạt động bình thường
  - `CRITICAL`: Lỗi nghiêm trọng dẫn đến dừng ứng dụng

## Tài Nguyên Hữu Ích

### Tài Liệu API

- [Selenium API](https://selenium-python.readthedocs.io/)
- [Supabase Python Client](https://github.com/supabase-community/supabase-py)
- [BeautifulSoup Documentation](https://www.crummy.com/software/BeautifulSoup/bs4/doc/)
- [Google Gemini AI](https://ai.google.dev/docs)
- [PyQt5 Documentation](https://www.riverbankcomputing.com/static/Docs/PyQt5/)

### Công Cụ Và Tiện Ích

- **Debugging**: VSCode với Python extension
- **Kiểm Tra Code**:
  - [Black](https://black.readthedocs.io/) cho định dạng code
  - [isort](https://pycqa.github.io/isort/) cho sắp xếp import
  - [flake8](https://flake8.pycqa.org/) cho linting
- **Kiểm Thử**: [pytest](https://docs.pytest.org/) 