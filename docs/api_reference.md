# Tài Liệu Tham Khảo API

Tài liệu này mô tả chi tiết các API của các dịch vụ và module chính trong ứng dụng Fetch Yahoo Auctions.

## Services

### YahooAuctionService

```python
from services.yahoo_auction_service import YahooAuctionService

# Khởi tạo
yahoo_service = YahooAuctionService(driver, supabase, genai_client)
```

#### `fetch_won_auctions()`

L<PERSON>y danh sách các đấu giá đã thắng từ Yahoo Auction.

**Tham số**: Không có

**Trả về**: 
- `List[Dict]`: Danh sách các đấu giá đã thắng

**Ví dụ**:
```python
auctions = yahoo_service.fetch_won_auctions()
print(f"Tìm thấy {len(auctions)} đấu giá đã thắng")
```

#### `process_auction_data(auctions)`

X<PERSON> lý và lưu trữ dữ liệu đấu giá vào cơ sở dữ liệu.

**Tham số**:
- `auctions (List[Dict])`: <PERSON>h sách các đấu giá đã thắng

**Trả về**: Không có

**Ví dụ**:
```python
yahoo_service.process_auction_data(auctions)
```

#### `process_pending_auctions()`

Xử lý các đấu giá đang chờ xử lý.

**Tham số**: Không có

**Trả về**: Không có

**Ví dụ**:
```python
yahoo_service.process_pending_auctions()
```

#### `process_feedback()`

Xử lý phản hồi cho các đấu giá đã hoàn thành.

**Tham số**: Không có  

**Trả về**: Không có

**Ví dụ**:
```python
yahoo_service.process_feedback()
```

### TrackingService

```python
from services.tracking_service import TrackingService

# Khởi tạo
tracking_service = TrackingService(driver, supabase)
```

#### `update_all_tracking()`

Cập nhật thông tin theo dõi cho tất cả các gói hàng.

**Tham số**: Không có

**Trả về**: Không có

**Ví dụ**:
```python
tracking_service.update_all_tracking()
```

#### `update_tracking(auction_id, tracking_number, service_name)`

Cập nhật thông tin theo dõi cho một đấu giá cụ thể.

**Tham số**:
- `auction_id (str)`: ID của đấu giá
- `tracking_number (str)`: Mã theo dõi gói hàng
- `service_name (str)`: Tên dịch vụ vận chuyển

**Trả về**:
- `Dict`: Thông tin theo dõi được cập nhật hoặc `None` nếu không tìm thấy

**Ví dụ**:
```python
result = tracking_service.update_tracking(
    auction_id="123",
    tracking_number="1234567890",
    service_name="ヤマト運輸"
)
```

### InvoiceService

```python
from services.invoice_service import InvoiceService

# Khởi tạo
invoice_service = InvoiceService(driver, supabase_client)
```

#### `download_invoice(auction_id, contact_url, plan_id)`

Tải xuống hóa đơn cho một đấu giá cụ thể.

**Tham số**:
- `auction_id (str)`: ID của đấu giá
- `contact_url (str)`: URL liên hệ với người bán
- `plan_id (int)`: ID của kế hoạch mua hàng

**Trả về**:
- `str`: Đường dẫn đến file hóa đơn đã tạo hoặc `None` nếu không thành công

**Ví dụ**:
```python
invoice_path = invoice_service.download_invoice(
    auction_id="a123456789",
    contact_url="https://contact.auctions.yahoo.co.jp/...",
    plan_id=1
)
```

#### `download_all_invoices(plan_id)`

Tải xuống hóa đơn cho tất cả các đấu giá đã hoàn thành trong một kế hoạch.

**Tham số**:
- `plan_id (int)`: ID của kế hoạch mua hàng

**Trả về**:
- `List[str]`: Danh sách đường dẫn đến các file hóa đơn đã tạo

**Ví dụ**:
```python
invoice_paths = invoice_service.download_all_invoices(plan_id=1)
```

#### `process_invoices(plan_id)`

Xử lý hóa đơn bằng cách chỉ giữ lại trang đầu tiên và gộp chúng lại.

**Tham số**:
- `plan_id (int)`: ID của kế hoạch mua hàng

**Trả về**:
- `str`: Đường dẫn đến file hóa đơn đã gộp

**Ví dụ**:
```python
merged_invoice_path = invoice_service.process_invoices(plan_id=1)
```

#### `calculate_total_cost(plan_ids=None)`

Tính tổng chi phí cho các kế hoạch được chỉ định.

**Tham số**:
- `plan_ids (List[int], optional)`: Danh sách ID của các kế hoạch mua hàng

**Trả về**:
- `Dict`: Thông tin chi tiết về tổng chi phí bao gồm:
  - `total`: Tổng chi phí (JPY)
  - `tracking_numbers`: Danh sách các mã theo dõi
  - `auction_count`: Số lượng đấu giá
  - `expense_count`: Số lượng chi phí bổ sung
  - `balance_jpy`: Số dư JPY
  - `balance_vnd`: Số dư VND
  - `log_messages`: Thông điệp nhật ký

**Ví dụ**:
```python
cost_details = invoice_service.calculate_total_cost(plan_ids=[1, 2])
print(f"Tổng chi phí: ¥{cost_details['total']:,}")
```

#### `get_exchange_rate(currency="VND")`

Lấy tỷ giá hối đoái mới nhất từ cơ sở dữ liệu.

**Tham số**:
- `currency (str, optional)`: Loại tiền tệ (mặc định là "VND")

**Trả về**:
- `float`: Tỷ giá hối đoái

**Ví dụ**:
```python
rate = invoice_service.get_exchange_rate()
print(f"Tỷ giá VND/JPY: {rate}")
```

#### `calculate_total_in_currency(total_jpy, currency="VND")`

Tính tổng chi phí theo loại tiền tệ khác.

**Tham số**:
- `total_jpy (float)`: Tổng chi phí (JPY)
- `currency (str, optional)`: Loại tiền tệ (mặc định là "VND")

**Trả về**:
- `float`: Tổng chi phí theo loại tiền tệ được chỉ định

**Ví dụ**:
```python
total_vnd = invoice_service.calculate_total_in_currency(10000)
print(f"Tổng chi phí: {total_vnd:,.0f} VND")
```

## Utils

### selenium_utils

#### `initialize_driver()`

Khởi tạo và trả về một instance của Chrome WebDriver.

**Tham số**: Không có

**Trả về**:
- `WebDriver`: Instance của Chrome WebDriver

**Ví dụ**:
```python
from utils.selenium_utils import initialize_driver

driver = initialize_driver()
```

#### `login_to_yahoo(driver)`

Đăng nhập vào Yahoo Japan.

**Tham số**:
- `driver (WebDriver)`: Instance của WebDriver

**Trả về**:
- `bool`: True nếu đăng nhập thành công, False nếu thất bại

**Ví dụ**:
```python
from utils.selenium_utils import login_to_yahoo

if login_to_yahoo(driver):
    print("Đăng nhập thành công")
else:
    print("Đăng nhập thất bại")
```

### data_utils

#### `process_html_data(html_content, selector)`

Xử lý và trích xuất dữ liệu từ HTML.

**Tham số**:
- `html_content (str)`: Nội dung HTML
- `selector (str)`: CSS selector

**Trả về**:
- `List[str]`: Dữ liệu đã trích xuất

**Ví dụ**:
```python
from utils.data_utils import process_html_data

data = process_html_data(html_content, "div.auction-item")
```

#### `save_to_db(supabase, table_name, data)`

Lưu dữ liệu vào bảng Supabase.

**Tham số**:
- `supabase (Client)`: Client Supabase
- `table_name (str)`: Tên bảng
- `data (Dict)`: Dữ liệu cần lưu

**Trả về**:
- `Dict`: Kết quả từ API

**Ví dụ**:
```python
from utils.data_utils import save_to_db

result = save_to_db(supabase, "auctions", {
    "name": "Sản phẩm A",
    "price": 1000,
    "url": "https://page.auctions.yahoo.co.jp/jp/auction/abcdef"
})
```

## CLI Commands

Ứng dụng cung cấp các lệnh dòng lệnh sau:

```bash
# Lấy đấu giá đã thắng
python main.py --fetch

# Xử lý đấu giá đang chờ
python main.py --process

# Xử lý phản hồi
python main.py --feedback

# Cập nhật thông tin vận chuyển
python main.py --track

# Chạy tất cả các thao tác
python main.py --all
```

## Cấu Trúc Supabase

### Bảng `auctions`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | text | ID duy nhất của đấu giá (từ Yahoo Auctions) |
| created_at | timestamp | Thời điểm tạo bản ghi |
| name | text | Tên sản phẩm đấu giá |
| auction_end_time | timestamp | Thời gian kết thúc đấu giá |
| url | text | URL của trang đấu giá |
| contact_url | text | URL liên hệ với người bán |
| price | integer | Giá thắng đấu giá (JPY) |
| delivery_method | text | Phương thức giao hàng |
| delivery_date | date | Ngày giao hàng dự kiến |
| delivery_time | text | Thời gian giao hàng dự kiến |
| delivery_status | text | Trạng thái giao hàng |
| tracking_number | text | Mã theo dõi gói hàng |
| status | text | Trạng thái xử lý của đấu giá |
| payment_total | integer | Tổng thanh toán |
| shipping_fee | text | Phí vận chuyển |
| invoice_url | text | URL hóa đơn |
| payment_method | text | Phương thức thanh toán |
| cod | integer | Phí thu hộ |
| plan_id | bigint | ID của kế hoạch mua hàng |
| buy_from | text | Nguồn mua hàng |
| reviewed_at | timestamp | Thời gian đánh giá |
| scanned_at | timestamp | Thời gian quét |

**Ví dụ truy vấn**:
```python
# Lấy tất cả đấu giá thuộc kế hoạch có ID là 1
auctions = supabase.table("auctions") \
    .select("*") \
    .eq("plan_id", 1) \
    .execute()

# Lọc kết hợp plan_id và trạng thái
delivered_auctions = supabase.table("auctions") \
    .select("*") \
    .eq("plan_id", 1) \
    .eq("status", "delivered") \
    .execute()
```

### Bảng `additional_expenses`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của chi phí |
| created_at | timestamp | Thời điểm tạo bản ghi |
| plan_id | bigint | ID của kế hoạch mua hàng |
| name | text | Tên chi phí phát sinh |
| amount | integer | Số tiền chi phí (JPY) |

**Ví dụ truy vấn**:
```python
# Thêm chi phí mới
result = supabase.table("additional_expenses").insert({
    "plan_id": 1,
    "name": "Phí bảo hiểm",
    "amount": 2000
}).execute()

# Lấy tổng chi phí phát sinh theo kế hoạch
expenses = supabase.table("additional_expenses") \
    .select("*") \
    .eq("plan_id", 1) \
    .execute()
```

### Bảng `exchanges`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của tỷ giá |
| created_at | timestamp | Thời điểm tạo bản ghi |
| vnd | integer | Số tiền VND |
| jpy | integer | Số tiền JPY |
| rate | real | Tỷ giá quy đổi |
| exchanged_at | date | Ngày áp dụng tỷ giá |
| memo | text | Ghi chú |

**Ví dụ truy vấn**:
```python
# Thêm tỷ giá mới
result = supabase.table("exchanges").insert({
    "vnd": 10000000,
    "jpy": 50000,
    "rate": 200.0,
    "exchanged_at": "2023-01-01"
}).execute()

# Lấy tỷ giá mới nhất
latest_exchange = supabase.table("exchanges") \
    .select("*") \
    .order("exchanged_at", option="desc") \
    .limit(1) \
    .execute()
```

### Bảng `plans`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của kế hoạch |
| created_at | timestamp | Thời điểm tạo bản ghi |
| start_date | date | Ngày bắt đầu kế hoạch |
| end_date | date | Ngày kết thúc kế hoạch |
| memo | text | Ghi chú về kế hoạch |
| status | smallint | Trạng thái kế hoạch (1: hoạt động, 0: đã đóng) |
| is_default | boolean | Đánh dấu là kế hoạch mặc định |

**Ví dụ truy vấn**:
```python
# Tạo kế hoạch mới
result = supabase.table("plans").insert({
    "start_date": "2023-01-01",
    "end_date": "2023-01-31",
    "memo": "Kế hoạch tháng 1/2023",
    "status": 1,
    "is_default": False
}).execute()

# Lấy kế hoạch mặc định
default_plan = supabase.table("plans") \
    .select("*") \
    .eq("is_default", True) \
    .execute()
```

## Trạng Thái Dữ Liệu

### Trạng Thái Xử Lý (`status`)
- `updating`: Mới thắng đấu giá, đang cập nhật
- `processing`: Đang xử lý
- `shipped`: Đã gửi hàng
- `delivered`: Đã nhận hàng
- `completed`: Hoàn tất

### Trạng Thái Vận Chuyển (`delivery_status`)
- `preparing`: Đang chuẩn bị gói hàng
- `in_transit`: Đang vận chuyển
- `out_for_delivery`: Đang giao hàng
- `delivered`: Đã giao hàng
- `unknown`: Không xác định 