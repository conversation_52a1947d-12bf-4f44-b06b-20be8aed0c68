# Tài Liệu Dự Án Fetch Yahoo Auctions

Thư mục này chứa tài liệu cho dự án Fetch Yahoo Auctions. Tài liệu được chia thành nhiều phần để giúp bạn hiểu cấu trúc, chức năng và cách phát triển dự án.

## Nội Dung

1. [**Cấu Trúc Dự Án**](project_structure.md) - Tổng quan về cấu trúc thư mục và mô tả các module chính
2. [**Luồng Xử Lý Dữ Liệu**](data_flow.md) - Mô tả chi tiết cách dữ liệu di chuyển qua hệ thống
3. [**Hướng Dẫn Phát Triển**](development.md) - Quy ước code, mẫu và hướng dẫn phát triển
4. [**Tài <PERSON>ả<PERSON> API**](api_reference.md) - Chi tiết về các API của dịch vụ và module

## Khởi <PERSON><PERSON>u <PERSON>

Để bắt đầu với dự án:

1. <PERSON><PERSON><PERSON> [C<PERSON>u Trúc Dự Án](project_structure.md) để hiểu tổng quan.
2. Thiết lập môi trường theo hướng dẫn trong [Hướng Dẫn Phát Triển](development.md).
3. Tìm hiểu [Luồng Xử Lý Dữ Liệu](data_flow.md) để hiểu cách hệ thống hoạt động.
4. Sử dụng [Tài Liệu Tham Khảo API](api_reference.md) khi phát triển và tích hợp với các dịch vụ.

## Cập Nhật Tài Liệu

Tài liệu nên được cập nhật khi:

- Thêm các module mới
- Thay đổi cấu trúc dự án
- Sửa đổi luồng dữ liệu
- Thêm hoặc thay đổi các tính năng chính
- Thay đổi API của các dịch vụ

## Liên Kết Ngoài

- [GitHub Repository](https://github.com/your-username/fetch-yahoo-auctions)
- [Tài Liệu Yahoo Auction API](https://developer.yahoo.co.jp/auctions/) (Tiếng Nhật)
- [Tài Liệu Supabase](https://supabase.com/docs/) 