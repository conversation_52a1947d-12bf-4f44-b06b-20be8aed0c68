# Luồng Xử Lý Dữ Liệu

## Tổng Quan

Tài liệu này mô tả chi tiết cách dữ liệu được xử lý trong hệ thống quản lý đấu giá Yahoo Auction, từ việc lấy thông tin đấu giá đến việc theo dõi gói hàng và tạo hóa đơn.

## Quy Trình Hoàn Chỉnh

```mermaid
graph TD
    A[Đăng nhập Yahoo Auction] --> B[Lấy danh sách đấu giá đã thắng]
    B --> C[Lưu dữ liệu đấu giá vào Supabase]
    C --> D[Xử lý chi tiết đấu giá]
    D --> E[Lấy thông tin vận chuyển]
    E --> F[<PERSON> dõi trạng thái giao hàng]
    F --> G[Xử lý phản hồi]
    G --> H[Tạo hóa đơn]
```

## Chi Tiết Các Bước

### 1. <PERSON><PERSON><PERSON> Danh Sách Đấu Giá Đã Thắng

**Luồng dữ liệu:**
1. <PERSON><PERSON><PERSON> nhập vào Yahoo Auction bằng Selenium
2. Điều hướng đến trang "Đấu giá đã thắng"
3. Phân tích HTML để trích xuất thông tin đấu giá
4. Chuyển đổi dữ liệu thô thành đối tượng có cấu trúc

**File liên quan:**
- `services/yahoo_auction_service.py`: `fetch_won_auctions()`
- `utils/selenium_utils.py`: `login_to_yahoo()`

### 2. Lưu Dữ Liệu Đấu Giá

**Luồng dữ liệu:**
1. Kiểm tra xem đấu giá đã tồn tại trong cơ sở dữ liệu chưa
2. Lọc ra chỉ những đấu giá mới
3. Chèn dữ liệu đấu giá mới vào bảng `auctions` trong Supabase

**File liên quan:**
- `services/yahoo_auction_service.py`: `process_auction_data()`
- `utils/data_utils.py`: Các hàm xử lý dữ liệu

### 3. Xử Lý Chi Tiết Đấu Giá

**Luồng dữ liệu:**
1. Truy vấn danh sách đấu giá "đang chờ xử lý" từ cơ sở dữ liệu
2. Cho mỗi đấu giá, điều hướng đến trang chi tiết
3. Trích xuất thông tin bổ sung (người bán, phí vận chuyển, v.v.)
4. Cập nhật bản ghi đấu giá với thông tin chi tiết

**File liên quan:**
- `services/yahoo_auction_service.py`: `process_pending_auctions()`

### 4. Lấy Thông Tin Vận Chuyển

**Luồng dữ liệu:**
1. Truy vấn các đấu giá cần thông tin vận chuyển
2. Điều hướng đến trang chi tiết đấu giá
3. Trích xuất mã theo dõi và dịch vụ vận chuyển
4. Cập nhật bản ghi đấu giá với thông tin vận chuyển

**File liên quan:**
- `services/yahoo_auction_service.py`: `process_pending_auctions()`
- `utils/data_utils.py`: Các hàm phân tích cú pháp HTML

### 5. Theo Dõi Trạng Thái Giao Hàng

**Luồng dữ liệu:**
1. Truy vấn các đấu giá có mã theo dõi
2. Cho mỗi đấu giá, xác định dịch vụ vận chuyển
3. Gọi API theo dõi tương ứng với dịch vụ vận chuyển
4. Phân tích kết quả để xác định trạng thái hiện tại
5. Cập nhật bản ghi đấu giá với trạng thái vận chuyển

**File liên quan:**
- `services/tracking_service.py`: `update_all_tracking()`

### 6. Xử Lý Phản Hồi

**Luồng dữ liệu:**
1. Truy vấn các đấu giá đã nhận hàng và cần gửi phản hồi
2. Điều hướng đến trang phản hồi
3. Gửi phản hồi tích cực
4. Cập nhật trạng thái phản hồi trong bản ghi đấu giá

**File liên quan:**
- `services/yahoo_auction_service.py`: `process_feedback()`

### 7. Tạo và Xử lý Hóa Đơn

**Luồng dữ liệu:**
1. **Tải hóa đơn cho từng đấu giá:**
   - Truy vấn các đấu giá đã hoàn thành trong một kế hoạch
   - Với mỗi đấu giá, điều hướng đến URL liên hệ và tìm trang chi tiết thanh toán
   - Sử dụng Chrome DevTools Protocol để tạo PDF từ trang web
   - Lưu PDF vào thư mục `invoices/{plan_id}/`

2. **Xử lý hóa đơn:**
   - Với mỗi hóa đơn, chỉ giữ lại trang đầu tiên (thông tin thanh toán)
   - Lưu các trang đã xử lý vào thư mục `invoices/processed/`
   - Gộp tất cả các trang đã xử lý thành một file PDF duy nhất
   - Lưu file PDF đã gộp vào `invoices/{plan_id}/Invoices_{plan_id}.pdf`

3. **Tính toán chi phí:**
   - Truy vấn tất cả đấu giá và chi phí bổ sung cho các kế hoạch
   - Tính tổng giá mua, phí vận chuyển và phí thu hộ (COD)
   - Loại bỏ các mã theo dõi trùng lặp để tránh tính toán hai lần
   - Thêm các chi phí bổ sung
   - Tính toán số dư JPY và VND dựa trên dữ liệu tỷ giá hối đoái
   - Tạo báo cáo chi tiết về các chi phí và số dư

**File liên quan:**
- `services/invoice_service.py`: `download_invoice()`, `download_all_invoices()`, `process_invoices()`, `calculate_total_cost()`, `get_exchange_rate()`, `calculate_total_in_currency()`

## Mô Hình Dữ Liệu

### Bảng `auctions`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | text | ID duy nhất của đấu giá (từ Yahoo Auctions) |
| created_at | timestamp | Thời điểm tạo bản ghi |
| name | text | Tên sản phẩm đấu giá |
| auction_end_time | timestamp | Thời gian kết thúc đấu giá |
| url | text | URL của trang đấu giá |
| contact_url | text | URL liên hệ với người bán |
| price | integer | Giá thắng đấu giá (JPY) |
| delivery_method | text | Phương thức giao hàng |
| delivery_date | date | Ngày giao hàng dự kiến |
| delivery_time | text | Thời gian giao hàng dự kiến |
| delivery_status | text | Trạng thái giao hàng |
| tracking_number | text | Mã theo dõi gói hàng |
| status | text | Trạng thái xử lý của đấu giá |
| payment_total | integer | Tổng thanh toán |
| shipping_fee | text | Phí vận chuyển |
| invoice_url | text | URL hóa đơn |
| payment_method | text | Phương thức thanh toán |
| cod | integer | Phí thu hộ |
| plan_id | bigint | ID của kế hoạch mua hàng |
| buy_from | text | Nguồn mua hàng |
| reviewed_at | timestamp | Thời gian đánh giá |
| scanned_at | timestamp | Thời gian quét |

### Bảng `additional_expenses`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của chi phí |
| created_at | timestamp | Thời điểm tạo bản ghi |
| plan_id | bigint | ID của kế hoạch mua hàng |
| name | text | Tên chi phí phát sinh |
| amount | integer | Số tiền chi phí (JPY) |

### Bảng `exchanges`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của tỷ giá |
| created_at | timestamp | Thời điểm tạo bản ghi |
| vnd | integer | Số tiền VND |
| jpy | integer | Số tiền JPY |
| rate | real | Tỷ giá quy đổi |
| exchanged_at | date | Ngày áp dụng tỷ giá |
| memo | text | Ghi chú |

### Bảng `plans`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của kế hoạch |
| created_at | timestamp | Thời điểm tạo bản ghi |
| start_date | date | Ngày bắt đầu kế hoạch |
| end_date | date | Ngày kết thúc kế hoạch |
| memo | text | Ghi chú về kế hoạch |
| status | smallint | Trạng thái kế hoạch (1: hoạt động, 0: đã đóng) |
| is_default | boolean | Đánh dấu là kế hoạch mặc định |

## Các Trạng Thái Dữ Liệu

### Trạng Thái Xử Lý (`status`)

- `updating`: Mới thắng đấu giá, đang cập nhật
- `processing`: Đang xử lý
- `shipped`: Đã gửi hàng
- `delivered`: Đã nhận hàng
- `completed`: Hoàn tất

### Trạng Thái Vận Chuyển (`delivery_status`)

- `preparing`: Đang chuẩn bị gói hàng
- `in_transit`: Đang vận chuyển
- `out_for_delivery`: Đang giao hàng
- `delivered`: Đã giao hàng
- `unknown`: Không xác định

## Lọc Và Hiển Thị Dữ Liệu

### Lọc Theo Plan ID

Dữ liệu đấu giá có thể được lọc theo `plan_id` để hiển thị các đấu giá thuộc về một kế hoạch mua hàng cụ thể. Điều này cho phép người dùng:
- Theo dõi tiến trình của từng kế hoạch mua hàng
- Tính toán tổng chi phí cho một kế hoạch
- Tạo hóa đơn cho các đấu giá trong cùng một kế hoạch

**Ví dụ truy vấn**:
```python
# Lấy tất cả đấu giá thuộc kế hoạch có ID là 1
auctions = supabase.table("auctions") \
    .select("*") \
    .eq("plan_id", 1) \
    .execute()

# Lọc kết hợp plan_id và trạng thái
delivered_auctions = supabase.table("auctions") \
    .select("*") \
    .eq("plan_id", 1) \
    .eq("status", "delivered") \
    .execute()
```

## Xử Lý Lỗi

Hệ thống xử lý các tình huống lỗi sau:

1. **Lỗi đăng nhập**: Lưu ảnh chụp màn hình vào thư mục `logs/` và thông báo lỗi
2. **Lỗi mạng**: Thử lại với backoff theo cấp số nhân
3. **Trang không tồn tại**: Ghi nhật ký và chuyển sang mục tiếp theo
4. **Mã theo dõi không hợp lệ**: Đánh dấu trạng thái vận chuyển là `unknown`
5. **Lỗi cơ sở dữ liệu**: Lưu trữ dữ liệu cục bộ và thử lại sau 