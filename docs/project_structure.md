# Cấu Trúc Dự Án Fetch Yahoo Auctions

## Tổng Quan

Dự án **Fetch Yahoo Auctions** là một ứng dụng tự động hóa quá trình quản lý đấu giá Yahoo Auction, bao gồm các chức năng:

- Lấy thông tin về các phiên đấu giá đã thắng
- Xử lý chi tiết đấu giá
- Gửi phản hồi (feedback)
- <PERSON> dõi trạng thái giao hàng
- Tạo hóa đơn

## Cấu Trúc Thư <PERSON>

```
fetch-yahoo-auctions/
├── data/                  # Tệp dữ liệu (cookies, v.v.)
├── docs/                  # Tài liệu dự án
├── logs/                  # Tệp nhật ký và ảnh chụp màn hình
├── scripts/               # Scripts tiện ích
├── services/              # Các module dịch vụ
│   ├── invoice_service.py # Dịch vụ tạo hóa đơn
│   ├── tracking_service.py # Dịch vụ theo dõi gói hàng
│   └── yahoo_auction_service.py # Dịch vụ API Yahoo Auction
├── utils/                 # Các hàm tiện ích
│   ├── data_utils.py      # Tiện ích xử lý dữ liệu
│   └── selenium_utils.py  # Tiện ích tự động hóa trình duyệt Selenium
├── invoices/              # Tệp hóa đơn tạo ra
├── .env                   # Biến môi trường
├── .env.example           # Mẫu biến môi trường
├── config.py              # Cấu hình thiết lập
├── main.py                # Giao diện dòng lệnh
├── run_ui.py              # Khởi chạy UI
├── ui.py                  # Giao diện người dùng
└── requirements.txt       # Phụ thuộc dự án
```

## Mô Tả Các Module Chính

### 1. Services

#### `yahoo_auction_service.py`
- **Chức năng**: Tương tác với Yahoo Auction để lấy và xử lý dữ liệu đấu giá
- **Phương thức chính**:
  - `fetch_won_auctions()`: Lấy danh sách đấu giá đã thắng
  - `process_auction_data()`: Xử lý dữ liệu đấu giá thô
  - `process_pending_auctions()`: Xử lý các đấu giá đang chờ xử lý
  - `process_feedback()`: Xử lý feedback cho người bán

#### `tracking_service.py`
- **Chức năng**: Theo dõi trạng thái giao hàng từ các dịch vụ vận chuyển
- **Phương thức chính**:
  - `update_all_tracking()`: Cập nhật thông tin theo dõi cho tất cả các gói hàng
  - Hỗ trợ nhiều dịch vụ vận chuyển: Yamato, Sagawa, Japan Post, v.v.

#### `invoice_service.py`
- **Chức năng**: Tạo và quản lý hóa đơn cho các đấu giá đã thắng
- **Phương thức chính**:
  - `download_invoice()`: Tải hóa đơn cho một đấu giá cụ thể
  - `download_all_invoices()`: Tải hóa đơn cho tất cả đấu giá trong một kế hoạch
  - `process_invoices()`: Xử lý và gộp hóa đơn
  - `calculate_total_cost()`: Tính tổng chi phí
  - `get_exchange_rate()`: Lấy tỷ giá hối đoái
  - `calculate_total_in_currency()`: Tính tổng theo loại tiền tệ khác

### 2. Utils

#### `selenium_utils.py`
- **Chức năng**: Cung cấp các hàm tiện ích cho việc tự động hóa trình duyệt với Selenium
- **Phương thức chính**:
  - `initialize_driver()`: Khởi tạo trình điều khiển Chrome
  - `login_to_yahoo()`: Đăng nhập vào Yahoo Japan
  - Các phương thức điều hướng và tương tác trang web

#### `data_utils.py`
- **Chức năng**: Xử lý và biến đổi dữ liệu
- **Phương thức chính**:
  - Phân tích dữ liệu từ HTML
  - Định dạng và xử lý chuỗi
  - Làm việc với cơ sở dữ liệu Supabase

### 3. Giao Diện

#### `main.py`
- **Chức năng**: Cung cấp giao diện dòng lệnh cho ứng dụng
- **Tùy chọn**:
  - `--fetch`: Lấy đấu giá đã thắng
  - `--process`: Xử lý đấu giá đang chờ
  - `--feedback`: Xử lý phản hồi
  - `--track`: Cập nhật thông tin theo dõi
  - `--all`: Chạy tất cả các tùy chọn

#### `ui.py` và `run_ui.py`
- **Chức năng**: Cung cấp giao diện đồ họa người dùng (GUI) với PyQt5
- **Tính năng**:
  - Trực quan hóa dữ liệu đấu giá
  - Quản lý đấu giá và gói hàng
  - Tạo và xem hóa đơn
  - Lọc đấu giá theo plan_id
  - Quản lý kế hoạch mua hàng

#### Chức năng Lọc Theo Kế Hoạch
- **Mô tả**: Cho phép người dùng lọc danh sách đấu giá theo kế hoạch mua hàng (plan_id)
- **Giao diện**:
  - Dropdown để chọn kế hoạch
  - Hiển thị tổng chi phí của kế hoạch được chọn
  - Phân loại đấu giá theo trạng thái trong mỗi kế hoạch
- **Lợi ích**:
  - Phân tách dữ liệu theo các chuyến đi mua hàng
  - Theo dõi ngân sách và chi tiêu cho từng kế hoạch
  - Tạo báo cáo và hóa đơn dựa trên kế hoạch cụ thể

## Công Nghệ Sử Dụng

- **Python**: Ngôn ngữ lập trình chính
- **Selenium**: Tự động hóa trình duyệt web
- **Supabase**: Cơ sở dữ liệu và xác thực
- **Google Gemini AI**: Xử lý ngôn ngữ tự nhiên
- **PyQt5**: Phát triển giao diện đồ họa người dùng
- **BeautifulSoup4**: Phân tích cú pháp HTML
- **PyPDF2**: Xử lý và tạo file PDF

## Cài Đặt và Sử Dụng

### Cài Đặt

1. Cài đặt các phụ thuộc:
```bash
pnpm install
```

2. Tạo tệp `.env` với các biến sau:
```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
GEMINI_API_KEY=your_gemini_api_key
YAHOO_USERNAME=your_yahoo_username
YAHOO_PASSWORD=your_yahoo_password
```

### Sử Dụng Command Line

```bash
# Lấy đấu giá đã thắng
python main.py --fetch

# Xử lý đấu giá đang chờ
python main.py --process

# Xử lý feedback
python main.py --feedback

# Cập nhật thông tin vận chuyển
python main.py --track

# Chạy tất cả các thao tác
python main.py --all
```

### Sử Dụng Giao Diện Đồ Họa

```bash
python run_ui.py
```

## Cấu Trúc Cơ Sở Dữ Liệu

Ứng dụng sử dụng Supabase làm cơ sở dữ liệu. Các bảng chính:

### Bảng `auctions`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | text | ID duy nhất của đấu giá (từ Yahoo Auctions) |
| created_at | timestamp | Thời điểm tạo bản ghi |
| name | text | Tên sản phẩm đấu giá |
| auction_end_time | timestamp | Thời gian kết thúc đấu giá |
| url | text | URL của trang đấu giá |
| contact_url | text | URL liên hệ với người bán |
| price | integer | Giá thắng đấu giá (JPY) |
| delivery_method | text | Phương thức giao hàng |
| delivery_date | date | Ngày giao hàng dự kiến |
| delivery_time | text | Thời gian giao hàng dự kiến |
| delivery_status | text | Trạng thái giao hàng |
| tracking_number | text | Mã theo dõi gói hàng |
| status | text | Trạng thái xử lý của đấu giá |
| payment_total | integer | Tổng thanh toán |
| shipping_fee | text | Phí vận chuyển |
| invoice_url | text | URL hóa đơn |
| payment_method | text | Phương thức thanh toán |
| cod | integer | Phí thu hộ |
| plan_id | bigint | ID của kế hoạch mua hàng |
| buy_from | text | Nguồn mua hàng |
| reviewed_at | timestamp | Thời gian đánh giá |
| scanned_at | timestamp | Thời gian quét |

### Bảng `additional_expenses`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của chi phí |
| created_at | timestamp | Thời điểm tạo bản ghi |
| plan_id | bigint | ID của kế hoạch mua hàng |
| name | text | Tên chi phí phát sinh |
| amount | integer | Số tiền chi phí (JPY) |

### Bảng `exchanges`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của tỷ giá |
| created_at | timestamp | Thời điểm tạo bản ghi |
| vnd | integer | Số tiền VND |
| jpy | integer | Số tiền JPY |
| rate | real | Tỷ giá quy đổi |
| exchanged_at | date | Ngày áp dụng tỷ giá |
| memo | text | Ghi chú |

### Bảng `plans`

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | bigint | ID duy nhất của kế hoạch |
| created_at | timestamp | Thời điểm tạo bản ghi |
| start_date | date | Ngày bắt đầu kế hoạch |
| end_date | date | Ngày kết thúc kế hoạch |
| memo | text | Ghi chú về kế hoạch |
| status | smallint | Trạng thái kế hoạch (1: hoạt động, 0: đã đóng) |
| is_default | boolean | Đánh dấu là kế hoạch mặc định |

## Trạng Thái Dữ Liệu

### Trạng Thái Xử Lý (`status`)
- `updating`: Mới thắng đấu giá, đang cập nhật
- `processing`: Đang xử lý
- `shipped`: Đã gửi hàng
- `delivered`: Đã nhận hàng
- `completed`: Hoàn tất

### Trạng Thái Vận Chuyển (`delivery_status`)
- `preparing`: Đang chuẩn bị gói hàng
- `in_transit`: Đang vận chuyển
- `out_for_delivery`: Đang giao hàng
- `delivered`: Đã giao hàng
- `unknown`: Không xác định

## Hỗ Trợ Dịch Vụ Vận Chuyển

Ứng dụng hỗ trợ theo dõi các gói hàng từ các công ty vận chuyển sau:

- ヤマト運輸 (Yamato Transport)
- 佐川急便 (Sagawa Express)
- 西濃運輸 (Seino Transportation)
- ゆうパック (Japan Post)
- 福山通運 (Fukuyama Transport)

## Kiểm Tra và Cập Nhật Xác Thực

Ứng dụng yêu cầu xác thực cho Yahoo Auctions. Bạn có thể kiểm tra và cập nhật cookie xác thực bằng cách sử dụng script sau:

```bash
python scripts/check_cookies.py 