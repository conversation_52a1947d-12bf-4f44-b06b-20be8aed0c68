#!/usr/bin/env python3
"""
Đơn giản hóa kiểm tra cookies Yahoo
"""

import os
import sys
import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By

# Thê<PERSON> thư mục gốc vào sys.path để import các module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import config

def load_cookies(filename='data/yahoo_cookies.json'):
    """Tải cookies từ file"""
    if not os.path.exists(filename):
        print(f"Không tìm thấy file cookie {filename}")
        return []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
            print(f"Đã tải {len(cookies)} cookies từ {filename}")
            return cookies
    except Exception as e:
        print(f"Lỗi khi tải cookies: {e}")
        return []

def save_cookies(cookies, filename='data/yahoo_cookies.json'):
    """Lưu cookies vào file"""
    try:
        # <PERSON><PERSON><PERSON> bả<PERSON> thư mục tồn tại
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, indent=4)
        print(f"Đã lưu {len(cookies)} cookies vào {filename}")
        return True
    except Exception as e:
        print(f"Lỗi khi lưu cookies: {e}")
        return False

def initialize_driver(headless=True):
    """Khởi tạo trình duyệt Chrome"""
    options = webdriver.ChromeOptions()
    options.add_argument('--no-sandbox')
    if headless:
        options.add_argument('--headless')
    
    options.add_argument('--disable-dev-shm-usage')
    
    service = Service()
    driver = webdriver.Chrome(service=service, options=options)
    return driver

def check_cookies():
    """Kiểm tra cookies hiện tại"""
    # Tải cookies
    cookies = load_cookies()
    if not cookies:
        print("Không có cookies để kiểm tra")
        return False
    
    # Khởi tạo trình duyệt ở chế độ headless trước
    driver = initialize_driver(headless=True)
    
    try:
        # Truy cập trang Yahoo
        print("Truy cập trang Yahoo...")
        driver.get("https://www.yahoo.co.jp")
        time.sleep(2)
        
        # Thêm cookies
        print("Thêm cookies vào trình duyệt...")
        for cookie in cookies:
            try:
                # Làm sạch cookie để tránh lỗi
                clean_cookie = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain', '.yahoo.co.jp'),
                    'path': cookie.get('path', '/'),
                }
                
                # Thêm expiry nếu có
                if 'expiry' in cookie:
                    clean_cookie['expiry'] = cookie['expiry']
                    
                driver.add_cookie(clean_cookie)
            except Exception as e:
                print(f"Lỗi khi thêm cookie {cookie['name']}: {e}")
        
        # Truy cập trang đấu giá đã thắng (cần đăng nhập)
        print("Truy cập trang đấu giá đã thắng...")
        driver.get(config.YAHOO_AUCTION_URL)
        time.sleep(5)
        
        # Kiểm tra URL hiện tại
        current_url = driver.current_url
        print(f"URL hiện tại: {current_url}")
        
        # Nếu URL chứa "login" hoặc "auth", cookies không hợp lệ
        if "login" in current_url or "auth" in current_url:
            print("Cookies không hợp lệ hoặc đã hết hạn (đã chuyển hướng đến trang đăng nhập)")
            return False
        else:
            print("Cookies hợp lệ (không chuyển hướng đến trang đăng nhập)")
            return True
            
    except Exception as e:
        print(f"Lỗi: {e}")
        return False
    finally:
        driver.quit()

def extract_new_cookies():
    """Đăng nhập thủ công và trích xuất cookies mới"""
    # Khởi tạo trình duyệt với giao diện người dùng để cho phép đăng nhập thủ công
    driver = initialize_driver(headless=False)
    
    try:
        # Truy cập trang đấu giá
        print("Truy cập trang đấu giá Yahoo...")
        driver.get(config.YAHOO_AUCTION_URL)
        time.sleep(3)
        
        # Kiểm tra xem có form đăng nhập không
        login_elements = driver.find_elements(By.ID, "login_handle")
        if login_elements:
            print("Tìm thấy form đăng nhập, vui lòng đăng nhập thủ công...")
            
            # Nhập username nếu có
            if config.YAHOO_USERNAME:
                login_elements[0].send_keys(config.YAHOO_USERNAME)
                print(f"Đã nhập username: {config.YAHOO_USERNAME}")
                
                # Tìm và nhấn nút tiếp theo
                try:
                    next_button = driver.find_element(By.XPATH, "//button[text()='次へ']")
                    next_button.click()
                    print("Đã nhấn nút tiếp theo")
                    time.sleep(2)
                except Exception as e:
                    print(f"Không tìm thấy nút tiếp theo: {e}")
        else:
            print("Không tìm thấy form đăng nhập, có thể đã đăng nhập sẵn")

        # Chờ người dùng đăng nhập thủ công
        print("\nVui lòng hoàn thành quá trình đăng nhập thủ công...")
        print("Bạn có 2 phút để hoàn thành quá trình đăng nhập.")
        print("Hãy nhập mã xác thực và hoàn thành tất cả các bước.")
        input("Nhấn Enter sau khi đã đăng nhập thành công...")
        
        # Kiểm tra xem đã đăng nhập thành công chưa
        current_url = driver.current_url
        print(f"URL hiện tại: {current_url}")
        
        if "login" in current_url or "auth" in current_url:
            print("Đăng nhập không thành công, vẫn ở trang đăng nhập")
            return False
        
        # Trích xuất cookies
        cookies = driver.get_cookies()
        print(f"Đã trích xuất {len(cookies)} cookies")
        
        # Lưu cookies
        if save_cookies(cookies):
            print("Đã lưu cookies mới thành công")
            return True
        else:
            print("Lỗi khi lưu cookies mới")
            return False
            
    except Exception as e:
        print(f"Lỗi: {e}")
        return False
    finally:
        driver.quit()

def main():
    """Hàm chính"""
    print("=== Kiểm tra Cookies Yahoo ===")
    
    # Kiểm tra cookies hiện tại
    if check_cookies():
        print("Cookies hiện tại vẫn còn hợp lệ!")
    else:
        print("Cookies hiện tại không hợp lệ hoặc đã hết hạn.")
        print("Bạn có muốn đăng nhập và trích xuất cookies mới không? (y/n)")
        choice = input("> ")
        
        if choice.lower() == 'y':
            # Đã mở browser ở chế độ không headless để đăng nhập thủ công
            if extract_new_cookies():
                print("Đã cập nhật cookies thành công!")
            else:
                print("Không thể cập nhật cookies.")
        else:
            print("Đã hủy cập nhật cookies.")

if __name__ == "__main__":
    main()
