# Yahoo Auction Manager

This application automates the process of managing Yahoo Auction purchases, including:

- Fetching won auctions
- Processing auction details
- Submitting feedback
- Tracking package delivery status
- Generating invoices

## Project Structure

The project is organized into the following directories:

```
yahoo-auction-manager/
├── data/                  # Data files (cookies, etc.)
├── logs/                  # Log files and screenshots
├── scripts/               # Utility scripts
├── services/              # Service modules
│   ├── invoice_service.py # Invoice generation service
│   ├── tracking_service.py # Package tracking service
│   └── yahoo_auction_service.py # Yahoo Auction API service
├── utils/                 # Utility functions
│   ├── data_utils.py      # Data handling utilities
│   └── selenium_utils.py  # Selenium browser automation utilities
├── invoices/              # Generated invoice files
├── .env                   # Environment variables
├── config.py              # Configuration settings
├── main.py                # Command-line interface
├── run_ui.py              # UI launcher
└── ui.py                  # User interface
```

## Setup

1. Install dependencies:
```
pip install -r requirements.txt
```

2. Create a `.env` file with the following variables:
```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
GEMINI_API_KEY=your_gemini_api_key
YAHOO_USERNAME=your_yahoo_username
YAHOO_PASSWORD=your_yahoo_password
```

## Usage

### Command Line Interface

The service can be run with the following commands:

```
# Fetch won auctions
python main.py --fetch

# Process pending auctions (get details, tracking numbers, etc.)
python main.py --process

# Process feedback for completed deliveries
python main.py --feedback

# Update tracking information
python main.py --track

# Run all operations
python main.py --all
```

### Graphical User Interface

To launch the graphical user interface:

```
python run_ui.py
```

## Authentication

The application requires authentication for Yahoo Auctions. You can check and update your authentication cookies using the following script:

```
python scripts/check_cookies.py
```

This script will:
1. Check if your current cookies are valid
2. If not, guide you through the login process to obtain new cookies
3. Save the new cookies for future use

## Database Structure

The application uses Supabase as a database. The following tables are used:

- `auctions`: Stores information about won auctions
- `additional_expenses`: Stores additional expenses related to auctions
- `exchanges`: Stores exchange rate information

## Tracking Support

The application supports tracking packages from the following delivery companies:

- ヤマト運輸 (Yamato Transport)
- 佐川急便 (Sagawa Express)
- 西濃運輸 (Seino Transportation)
- ゆうパック (Japan Post)
- 福山通運 (Fukuyama Transport)
