"""
Main entry point for the market research module.
"""

import argparse
import sys
import os
import json
import datetime
from pathlib import Path

# Add parent directory to path to allow importing from modules
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

# Import services and models
try:
    # Try relative imports when used as a package
    from .services.gemini_service import gemini_service
    from .models.product_analyzer import product_analyzer
    from .models.product_scanner import product_scanner
    from .services.yahoo_auction_service import yahoo_auction_service
    from .services.supabase_service import supabase_service
    from .utils.json_storage import JSONStorage
    from .utils.data_processor import data_processor
except ImportError:
    # Fall back to direct imports when run as script
    from services.gemini_service import gemini_service
    from models.product_analyzer import product_analyzer
    from models.product_scanner import product_scanner
    from services.yahoo_auction_service import yahoo_auction_service
    from services.supabase_service import supabase_service
    from utils.json_storage import JSONStorage
    from utils.data_processor import data_processor

# Define file paths for storing data
SCANNED_PRODUCTS_FILE = "scanned_products.json"
COMMON_PATTERNS_FILE = "common_patterns.json"
RECOMMENDATIONS_FILE = "recommendations.json"
PRODUCT_CATEGORIES_FILE = "product_categories.json"


def analyze_purchase_history():
    """
    Analyze purchase history to find common patterns.
    """
    print("購入履歴を分析中...")
    
    # Get purchase history from Supabase (real data)
    try:
        purchase_history = supabase_service.get_purchase_history()
        print(f"Supabaseから{len(purchase_history)}件の購入履歴を取得しました")
    except Exception as e:
        print(f"Supabaseからのデータ取得エラー: {str(e)}")
        # Fallback to Yahoo Auction service if Supabase fails
        purchase_history = yahoo_auction_service.get_purchase_history()
        print(f"Yahoo Auctionから{len(purchase_history)}件の購入履歴を取得しました")
    
    print(f"購入履歴の{len(purchase_history)}件の商品を分析中...")
    
    # Extract descriptions for analysis
    descriptions = [p.get("description", "") for p in purchase_history if p.get("description")]
    
    # Log some sample descriptions for debugging
    if descriptions:
        print(f"サンプル説明文: {descriptions[0][:100]}...")
    
    # Find common patterns in descriptions
    patterns = gemini_service.find_common_patterns(descriptions)
    
    # Save patterns to file
    patterns_storage = JSONStorage(COMMON_PATTERNS_FILE)
    patterns_storage.save_data([patterns])  # Save as array for consistency
    
    # Generate categories from patterns
    categories = product_analyzer.generate_product_categories(patterns)
    
    # Save categories to file
    categories_storage = JSONStorage(PRODUCT_CATEGORIES_FILE)
    categories_storage.save_data(categories)
    
    print(f"分析結果: {len(purchase_history)}件の商品を分析しました")
    
    # Print common patterns
    print("\n共通パターン:")
    if "common_keywords" in patterns:
        print(f"好みのキーワード: {', '.join(patterns.get('common_keywords', [])[:3])}")
    if "avoided_features" in patterns:
        print(f"避けるキーワード: {', '.join(patterns.get('avoided_features', [])[:3])}")
    
    # Calculate price range from purchase history
    prices = [p.get("price", 0) for p in purchase_history if p.get("price", 0) > 0]
    if prices:
        min_price = min(prices)
        max_price = max(prices)
        print(f"価格範囲: ¥{min_price:,} - ¥{max_price:,}")
    
    if "preferred_features" in patterns:
        print(f"品質指標: {', '.join(patterns.get('preferred_features', [])[:3])}")
    
    return {"analyzed_products": len(purchase_history), "common_patterns": patterns, "categories": categories}


def scan_new_products():
    """
    Scan for new products on Yahoo Auctions based on purchase history patterns.
    
    Returns:
        dict: Scan result data
    """
    print("新商品をスキャン中...")
    
    # Get common patterns from purchase history analysis
    common_patterns = JSONStorage(COMMON_PATTERNS_FILE).load_data()
    
    # Clear any existing scanned products to start fresh
    product_scanner.clear_scanned_products()
    
    # Get keywords from common patterns
    keywords = []
    
    if common_patterns and "common_keywords" in common_patterns[0]:
        keywords.extend(common_patterns[0].get("common_keywords", []))
    
    if common_patterns and "preferred_features" in common_patterns[0]:
        keywords.extend(common_patterns[0].get("preferred_features", []))
    
    # Make keywords unique
    keywords = list(set(keywords))
    
    # If no patterns found, use some common keywords related to chainsaws
    if not keywords:
        keywords = [
            "チェーンソー",
            "チェンソー",
            "エンジン",
            "STIHL",
            "スチール",
            "ゼノア",
            "ジャンク",
            "現状品",
            "要調整",
            "要整備"
        ]
    
    # Scan for new products using scanner's built-in keywords
    # This will use the keywords defined in ProductScanner.__init__
    scanned_products = product_scanner.scan_with_keywords()
    
    # Save the products to storage (even though scan_with_keywords also saves them)
    if scanned_products:
        product_scanner.save_scanned_products(scanned_products)
    
    # Get scanned products from storage to confirm they were saved
    stored_products = product_scanner.get_scanned_products()
    
    print(f"スキャン結果: {len(scanned_products)}件の新商品をスキャンしました\n")
    
    # Show a few examples
    print("新商品の例:")
    example_count = min(3, len(scanned_products))
    for product in scanned_products[:example_count]:  # Show top 3
        print(f"- {product.get('name', '')} (¥{product.get('price', 0):,})")
    
    # Print empty examples if no products
    for _ in range(3 - example_count):
        print(f"- No product found")
    
    print()
    
    return {
        "total_scanned": len(scanned_products),
        "scanned_products": scanned_products
    }


def evaluate_products():
    """
    Evaluate scanned products and generate recommendations.
    """
    print("\n商品を評価中...")
    
    # Load common patterns and scanned products
    patterns_storage = JSONStorage(COMMON_PATTERNS_FILE)
    common_patterns = patterns_storage.load_data()
    
    if not common_patterns:
        print("評価エラー: 共通パターンが見つかりません")
        return {"error": "共通パターンが見つかりません"}
    
    # Get the first pattern (most recent)
    pattern = common_patterns[0]
    
    # Get all scanned products
    all_products = product_scanner.get_scanned_products()
    
    # Lọc các sản phẩm có đủ thông tin
    valid_products = []
    for product in all_products:
        if product.get("name") and product.get("price") is not None and product.get("description"):
            valid_products.append(product)
    
    if not valid_products:
        print("評価エラー: 有効な商品データがありません")
        return {"error": "有効な商品データがありません"}
    
    print(f"{len(valid_products)}件の商品を評価中...")
    
    # Evaluate each product
    recommendations = []
    for product in valid_products:
        try:
            # Chuẩn bị dữ liệu cho Gemini AI
            evaluation = gemini_service.evaluate_product(product, pattern)
            
            if evaluation:
                # Đảm bảo evaluation có trường product chứa thông tin sản phẩm
                if "product" not in evaluation:
                    evaluation["product"] = product
                
                recommendations.append(evaluation)
        except Exception as e:
            print(f"商品評価エラー: {str(e)}")
    
    # Save recommendations
    recommendations_storage = JSONStorage(RECOMMENDATIONS_FILE)
    recommendations_storage.save_data(recommendations)
    
    print(f"評価結果: {len(recommendations)}件の商品を評価しました")
    
    # Print buy recommendations
    buy_recommendations = [r for r in recommendations if r.get("recommendation") == "buy"]
    
    if buy_recommendations:
        print("\n購入推奨商品:")
        for rec in buy_recommendations[:5]:  # Chỉ hiển thị 5 sản phẩm hàng đầu
            # Lấy thông tin sản phẩm
            product = rec.get('product', {})
            if not isinstance(product, dict):
                # Nếu product không phải dict, tạo dict từ thông tin trong rec
                product = {
                    "id": rec.get("product_id", ""),
                    "name": rec.get("product_name", ""),
                    "price": rec.get("product_price", 0),
                    "url": rec.get("product_url", "")
                }
            
            # Tránh lỗi khi giá là None
            product_price = product.get('price', 0) 
            if product_price is None:
                product_price = 0
            
            # Hiển thị thông tin sản phẩm
            print(f"- {product.get('name', '')} (¥{product_price:,})")
            print(f"  スコア: {rec.get('score', 0):.2f}")
            print(f"  理由: {rec.get('reasoning', '')[:100]}..." if len(rec.get('reasoning', '')) > 100 else f"  理由: {rec.get('reasoning', '')}")
            print(f"  URL: {product.get('url', '')}")
            print()
    else:
        print("\n購入推奨商品はありません")
    
    return {"total_evaluated": len(recommendations), "recommendations": recommendations}


def display_summary():
    """
    Display a summary of the market research data.
    """
    print("\n市場調査データのサマリー:")
    
    # Load data
    scanned_storage = JSONStorage(SCANNED_PRODUCTS_FILE)
    recommendations_storage = JSONStorage(RECOMMENDATIONS_FILE)
    patterns_storage = JSONStorage(COMMON_PATTERNS_FILE)
    categories_storage = JSONStorage(PRODUCT_CATEGORIES_FILE)
    
    scanned_products = scanned_storage.load_data()
    recommendations = recommendations_storage.load_data()
    patterns = patterns_storage.load_data()
    categories = categories_storage.load_data()
    
    # Print summary
    print(f"スキャンした商品: {len(scanned_products)}件")
    print(f"推奨商品: {len([r for r in recommendations if r.get('recommendation') == 'buy'])}件")
    print(f"スキップ推奨商品: {len([r for r in recommendations if r.get('recommendation') == 'skip'])}件")
    
    # Calculate average price
    prices = [p.get('price', 0) for p in scanned_products if p.get('price', 0) > 0]
    if prices:
        avg_price = sum(prices) / len(prices)
        print(f"平均価格: ¥{avg_price:,.0f}")
    
    # Show pattern categories if available
    if categories:
        print("\n商品カテゴリ:")
        for i, category in enumerate(categories[:3]):
            print(f"{i+1}. {category.get('name', '')}: {category.get('description', '')[:50]}...")


def parse_args():
    """
    Parse command line arguments.
    """
    parser = argparse.ArgumentParser(description="Yahoo Auctions Market Research Tool")
    parser.add_argument('--analyze', action='store_true', help='Analyze purchase history')
    parser.add_argument('--scan', action='store_true', help='Scan for new products')
    parser.add_argument('--evaluate', action='store_true', help='Evaluate scanned products')
    parser.add_argument('--summary', action='store_true', help='Display summary')
    parser.add_argument('--all', action='store_true', help='Run all steps')
    
    return parser.parse_args()


import time

def log_step(msg):
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {msg}")

def main():
    """
    Main function.
    """
    args = parse_args()
    
    log_step("Yahoo Auctions市場調査ツール 開始")
    print("=" * 40)
    
    # Create necessary JSON files if they don't exist
    for file_path in [SCANNED_PRODUCTS_FILE, COMMON_PATTERNS_FILE, RECOMMENDATIONS_FILE, PRODUCT_CATEGORIES_FILE]:
        storage = JSONStorage(file_path)
        if not storage.file_exists():
            storage.save_data([])
    
    # Check if Gemini API key is set
    if not os.environ.get("GEMINI_API_KEY"):
        log_step("警告: GEMINI_API_KEY環境変数が設定されていません。評価機能は利用できません。環境変数を設定するには: export GEMINI_API_KEY=your_api_key")
    
    if args.all or args.analyze:
        log_step("==== [STEP] analyze_purchase_history 開始 ====")
        analyze_purchase_history()
        log_step("==== [STEP] analyze_purchase_history 完了 ====")
    
    if args.all or args.scan:
        log_step("==== [STEP] scan_new_products 開始 ====")
        scan_new_products()
        log_step("==== [STEP] scan_new_products 完了 ====")
    
    if args.all or args.evaluate:
        log_step("==== [STEP] evaluate_products 開始 ====")
        evaluate_products()
        log_step("==== [STEP] evaluate_products 完了 ====")
    
    if args.all or args.summary:
        log_step("==== [STEP] display_summary 開始 ====")
        display_summary()
        log_step("==== [STEP] display_summary 完了 ====")
    
    if not (args.all or args.analyze or args.scan or args.evaluate or args.summary):
        print("\n使用方法: python main.py [options]")
        print("オプション:")
        print("  --analyze   購入履歴を分析")
        print("  --scan      新商品をスキャン")
        print("  --evaluate  スキャンした商品を評価")
        print("  --summary   データのサマリーを表示")
        print("  --all       すべてのステップを実行")
    log_step("Yahoo Auctions市場調査ツール 終了")


if __name__ == "__main__":
    main()
