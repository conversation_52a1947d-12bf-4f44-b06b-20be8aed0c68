"""
Product analyzer model for market research.
"""

import uuid
import datetime

# Thử import với cả absolute và relative path
try:
    # Relative imports
    from ..services.json_storage_service import json_storage
    from ..services.gemini_service import gemini_service
    from ..config import PRODUCT_CATEGORIES_FILE, COMMON_PATTERNS_FILE
except ImportError:
    try:
        # Absolute imports từ package
        from market_research.services.json_storage_service import json_storage
        from market_research.services.gemini_service import gemini_service
        from market_research.config import PRODUCT_CATEGORIES_FILE, COMMON_PATTERNS_FILE
    except ImportError:
        # Direct imports
        from services.json_storage_service import json_storage
        from services.gemini_service import gemini_service
        from config import PRODUCT_CATEGORIES_FILE, COMMON_PATTERNS_FILE


class ProductAnalyzer:
    """
    Model for analyzing products from purchase history.
    """

    def __init__(self):
        """
        Initialize the product analyzer.
        """
        pass

    def analyze_purchase_history(self, purchased_products):
        """
        Analyze purchase history to find patterns.
        
        Args:
            purchased_products: List of purchased products
            
        Returns:
            dict: Analysis results
        """
        if not purchased_products:
            return {"error": "No purchase history found"}
            
        print(f"購入履歴の{len(purchased_products)}件の商品を分析中...")
        
        # Analyze product descriptions using Gemini
        analysis_results = []
        
        for product in purchased_products[:20]:  # Limit to 20 products to avoid API limits
            if not product.get('description'):
                continue
                
            try:
                # Analyze product description
                description_analysis = gemini_service.analyze_product_description(
                    product.get('description', '')
                )
                
                analysis_results.append({
                    "product_id": product.get('id'),
                    "product_name": product.get('name'),
                    "analysis": description_analysis
                })
            except Exception as e:
                print(f"商品分析エラー: {str(e)}")
        
        # Generate common patterns from analysis results
        common_patterns = self._generate_common_patterns(analysis_results, purchased_products)
        
        # Save common patterns
        if common_patterns:
            json_storage.save_data([common_patterns], COMMON_PATTERNS_FILE)
            
        # Generate product categories
        categories = self._generate_categories(analysis_results)
        
        # Save categories
        if categories:
            json_storage.save_data(categories, PRODUCT_CATEGORIES_FILE)
        
        return {
            "analyzed_products": len(analysis_results),
            "common_patterns": common_patterns,
            "categories": categories
        }

    def _generate_common_patterns(self, analysis_results, purchased_products):
        """
        Generate common patterns from analysis results.
        
        Args:
            analysis_results: Analysis results
            purchased_products: List of purchased products
            
        Returns:
            dict: Common patterns
        """
        # Extract keywords
        all_keywords = []
        positive_indicators = []
        negative_indicators = []
        
        for result in analysis_results:
            analysis = result.get('analysis', {})
            
            # Extract keywords
            keywords = analysis.get('keywords', [])
            all_keywords.extend(keywords)
            
            # Extract quality indicators
            quality_indicators = analysis.get('quality_indicators', [])
            positive_indicators.extend(quality_indicators)
            
            # Extract warning signs
            warning_signs = analysis.get('warning_signs', [])
            negative_indicators.extend(warning_signs)
        
        # Count keyword frequencies
        keyword_counts = {}
        for keyword in all_keywords:
            if keyword in keyword_counts:
                keyword_counts[keyword] += 1
            else:
                keyword_counts[keyword] = 1
        
        # Get top keywords
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
        preferred_keywords = [k for k, v in top_keywords[:10] if v > 1]
        
        # Count quality indicator frequencies
        quality_counts = {}
        for indicator in positive_indicators:
            if indicator in quality_counts:
                quality_counts[indicator] += 1
            else:
                quality_counts[indicator] = 1
        
        # Get top quality indicators
        top_indicators = sorted(quality_counts.items(), key=lambda x: x[1], reverse=True)
        quality_indicators = [k for k, v in top_indicators[:5] if v > 1]
        
        # Count warning sign frequencies
        warning_counts = {}
        for warning in negative_indicators:
            if warning in warning_counts:
                warning_counts[warning] += 1
            else:
                warning_counts[warning] = 1
        
        # Get top warning signs
        top_warnings = sorted(warning_counts.items(), key=lambda x: x[1], reverse=True)
        avoided_keywords = [k for k, v in top_warnings[:5] if v > 0]
        
        # Calculate price range
        prices = [p.get('price', 0) for p in purchased_products if p.get('price', 0) > 0]
        if prices:
            min_price = min(prices)
            max_price = max(prices)
        else:
            min_price = 0
            max_price = 50000  # Default max price
        
        # Generate common patterns
        common_patterns = {
            "id": str(uuid.uuid4()),
            "created_at": datetime.datetime.now().isoformat(),
            "preferred_keywords": preferred_keywords,
            "avoided_keywords": avoided_keywords,
            "price_range": {
                "min": min_price,
                "max": max_price
            },
            "quality_indicators": quality_indicators
        }
        
        return common_patterns

    def _generate_categories(self, analysis_results):
        """
        Generate product categories from analysis results.
        
        Args:
            analysis_results: Analysis results
            
        Returns:
            list: Product categories
        """
        # Extract keywords from analysis results
        all_keywords = []
        
        for result in analysis_results:
            analysis = result.get('analysis', {})
            keywords = analysis.get('keywords', [])
            all_keywords.extend(keywords)
        
        # For now, we'll just use a simple approach
        # In a real implementation, we would use clustering or ask Gemini to categorize
        categories = [
            {
                "id": 1,
                "created_at": datetime.datetime.now().isoformat(),
                "name": "電子機器",
                "description": "電子機器、デジタル製品、ガジェット",
                "keywords": [k for k in all_keywords if k in ["電子", "デジタル", "ガジェット", "機器"]]
            },
            {
                "id": 2,
                "created_at": datetime.datetime.now().isoformat(),
                "name": "コレクション",
                "description": "コレクション、フィギュア、モデル",
                "keywords": [k for k in all_keywords if k in ["コレクション", "フィギュア", "モデル", "限定"]]
            }
        ]
        
        return categories
        
    def generate_product_categories(self, patterns):
        """
        Generate product categories from common patterns.
        
        Args:
            patterns: Common patterns data
            
        Returns:
            list: Product categories
        """
        # Default categories in case patterns don't have enough information
        default_categories = [
            {
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "ゲーム・コンソール",
                "description": "ゲームコンソール、ゲームソフト、アクセサリー",
                "keywords": ["ゲーム", "コンソール", "Nintendo", "PlayStation", "Xbox"]
            },
            {
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "電子機器",
                "description": "電子機器、デジタル製品、スマートフォン、タブレット",
                "keywords": ["電子機器", "スマートフォン", "タブレット", "デジタル"]
            },
            {
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "コレクション・古物",
                "description": "コレクション品、古物、フィギュア、モデル",
                "keywords": ["コレクション", "古物", "フィギュア", "限定"]
            }
        ]
        
        # If we don't have enough pattern data, return default categories
        if not patterns:
            return default_categories
        
        # Try to extract keywords from patterns
        keywords = []
        if "common_keywords" in patterns:
            keywords.extend(patterns["common_keywords"])
        if "preferred_features" in patterns:
            keywords.extend(patterns["preferred_features"])
        
        # If no keywords found, return defaults
        if not keywords:
            return default_categories
            
        # Create categories based on keywords
        categories = []
        
        # Check for game related keywords
        game_keywords = ["ゲーム", "コンソール", "Nintendo", "PlayStation", "Switch", "PS"]
        if any(kw in game_keywords for kw in keywords):
            categories.append({
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "ゲーム関連",
                "description": "ゲーム、コンソール、関連アクセサリー",
                "keywords": [k for k in keywords if k in game_keywords]
            })
        
        # Check for electronics keywords
        electronics_keywords = ["電子", "デジタル", "ガジェット", "スマートフォン", "タブレット"]
        if any(kw in electronics_keywords for kw in keywords):
            categories.append({
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "電子機器",
                "description": "電子機器、デジタル製品、ガジェット",
                "keywords": [k for k in keywords if k in electronics_keywords]
            })
        
        # Check for collection keywords
        collection_keywords = ["コレクション", "古物", "フィギュア", "限定", "モデル"]
        if any(kw in collection_keywords for kw in keywords):
            categories.append({
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "コレクション",
                "description": "コレクション品、古物、フィギュア",
                "keywords": [k for k in keywords if k in collection_keywords]
            })
        
        # If no categories were created, use default
        if not categories:
            # Create a generic category from keywords
            categories.append({
                "id": str(uuid.uuid4()),
                "created_at": datetime.datetime.now().isoformat(),
                "name": "お気に入り商品",
                "description": "購入履歴から分析されたお気に入りの商品カテゴリ",
                "keywords": keywords[:10]
            })
            
            # Add a default category as well
            categories.append(default_categories[0])
        
        return categories


# Singleton instance
product_analyzer = ProductAnalyzer()
