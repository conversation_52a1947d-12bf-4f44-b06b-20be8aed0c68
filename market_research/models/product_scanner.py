"""
Model for scanning new products from Yahoo Auctions.
"""

import json

# Thử import với cả absolute và relative path
try:
    # Relative imports
    from ..services.yahoo_auction_service import yahoo_auction_service
    from ..utils.json_storage import JSONStorage
except ImportError:
    try:
        # Absolute imports từ package
        from market_research.services.yahoo_auction_service import yahoo_auction_service
        from market_research.utils.json_storage import JSONStorage
    except ImportError:
        # Direct imports
        from services.yahoo_auction_service import yahoo_auction_service
        from utils.json_storage import JSONStorage


class ProductScanner:
    """
    Scanner for fetching and saving new products from Yahoo Auctions.
    """

    def __init__(self):
        """
        Initialize the product scanner.
        """
        # Thử import với cả absolute và relative path
        try:
            # Relative imports
            from ..config import SCANNED_PRODUCTS_FILE
        except ImportError:
            try:
                # Absolute imports từ package
                from market_research.config import SCANNED_PRODUCTS_FILE
            except ImportError:
                # Direct imports
                from config import SCANNED_PRODUCTS_FILE
                
        self.storage = JSONStorage(SCANNED_PRODUCTS_FILE)
        self.keywords = [
                "チェーンソー",  # Chainsaw
                "STIHL",  # Brand
                "ゼノア",  # Brand
                "ECHO",  # Brand
                "shindaiwa",  # Brand
                "ジャンク品",  # Junk item
                "ジャンク",  # Junk
                "現状品",  # As-is item
                "要調整",  # Needs adjustment
                "要整備",  # Needs maintenance
                "エンジン式",  # Engine type
                "チェーンソー 中古",  # Used chainsaw
                "チェーンソー ジャンク",  # Junk chainsaw
                "チェーンソー 現状",  # As-is chainsaw
                "STIHL 中古",  # Used STIHL
                "ゼノア 中古"  # Used Zenoah
            ]
    
    def scan_with_keywords(self, keywords=None, max_results=20):
        """
        Scan Yahoo Auctions for products matching keywords.
        
        Args:
            keywords: List of keywords to search for (default: self.keywords)
            max_results: Maximum results per keyword
        
        Returns:
            list: List of scanned products
        """
        print(f"Starting scan with {len(keywords or self.keywords)} keywords")
        
        all_products = []
        
        # Use default keywords if none provided
        if not keywords:
            keywords = self.keywords
            print(f"Using default keywords: {', '.join(keywords[:3])}... (total {len(keywords)} keywords)")
        
        # Scan for each keyword
        for keyword in keywords:
            print(f"Searching with keyword: {keyword}")
            product_ids = yahoo_auction_service.search_products(keyword, max_results)
            print(f"Found {len(product_ids)} product IDs for keyword '{keyword}'")
            
            # Get details for each product
            for product_id in product_ids:
                # Skip if already scanned
                existing_products = self.storage.load_data()
                existing_ids = [p.get("id") for p in existing_products]
                
                if product_id in existing_ids:
                    print(f"Skipping already scanned product: {product_id}")
                    continue
                
                print(f"Fetching details for product: {product_id}")
                product = yahoo_auction_service.get_product_details(product_id)
                
                if product and product.get("name") and product.get("price"):
                    print(f"Found valid product: {product.get('name')} (¥{product.get('price')})")
                    all_products.append(product)
                else:
                    print(f"Invalid product data for ID: {product_id}")
        
        # Save new products
        self.save_scanned_products(all_products)
        
        print(f"Scan completed. Found {len(all_products)} valid products")
        return all_products
    
    def scan_with_pattern(self, pattern, max_results=20):
        """
        Scan Yahoo Auctions using common patterns detected.
        
        Args:
            pattern: Dictionary of pattern data with keywords
            max_results: Maximum results to fetch
        
        Returns:
            list: List of scanned products
        """
        # Extract keywords from pattern
        keywords = []
        
        if "common_keywords" in pattern:
            keywords.extend(pattern["common_keywords"])
        
        if "preferred_features" in pattern:
            keywords.extend(pattern["preferred_features"])
        
        # Make keywords unique
        keywords = list(set(keywords))
        
        # Scan with extracted keywords
        return self.scan_with_keywords(keywords, max_results)
    
    def save_scanned_products(self, products):
        """
        Save scanned products to storage.
        
        Args:
            products: List of product data
        """
        print(f"Saving {len(products)} products to storage...")
        
        # Get existing products
        existing_products = self.storage.load_data()
        print(f"Found {len(existing_products)} existing products in storage")
        
        existing_ids = [p.get("id") for p in existing_products]
        
        # Add only new products
        new_products = [p for p in products if p.get("id") not in existing_ids]
        print(f"Found {len(new_products)} new products to save")
        
        if new_products:
            self.storage.append_data(new_products)
            print(f"Successfully saved {len(new_products)} new products")
        else:
            print("No new products to save")
    
    def get_scanned_products(self):
        """
        Get all scanned products from storage.
        
        Returns:
            list: Scanned products
        """
        return self.storage.load_data()
    
    def clear_scanned_products(self):
        """
        Clear all scanned products from storage.
        """
        self.storage.save_data([])
        
    def scan_products(self, keyword_list=None):
        """
        Scan for new products with a given list of keywords.
        
        Args:
            keyword_list: Optional list of keywords to search for
            
        Returns:
            list: List of scanned products
        """
        # If no keywords provided, use default keywords
        if not keyword_list:
            keyword_list = self.keywords
            
        # Call scan_with_keywords to get and save products
        return self.scan_with_keywords(keyword_list)


# Singleton instance
product_scanner = ProductScanner()
