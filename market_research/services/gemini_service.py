"""
Service for integrating with Google Gemini AI.
"""

import os
import json
import re
import google.generativeai as genai
from pathlib import Path
from dotenv import load_dotenv

# Try to load from .env file in the project root
try:
    # Find project root (parent of market_research directory)
    current_dir = Path(__file__).resolve().parent  # services directory
    market_research_dir = current_dir.parent  # market_research directory
    project_root = market_research_dir.parent  # project root directory
    
    # Load .env file from project root
    env_path = project_root / ".env"
    if env_path.exists():
        load_dotenv(dotenv_path=str(env_path))
        print(f"Loaded environment variables from {env_path}")
    else:
        print(f"Warning: .env file not found at {env_path}")
    
    # Get Gemini API key from environment
    GEMINI_API_KEY = os.environ.get("GEMINI_API_KEY")
    
    if GEMINI_API_KEY:
        print("Gemini API key loaded from .env file")
    else:
        print("Warning: GEMINI_API_KEY not found in environment variables")

except Exception as e:
    print(f"Error loading environment variables: {str(e)}")
    GEMINI_API_KEY = None


class GeminiService:
    """
    Service for interacting with Google Gemini AI.
    """

    def __init__(self):
        """
        Initialize the Gemini service.
        """
        self.api_key = os.environ.get("GEMINI_API_KEY")
        self.client = None
        
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY環境変数が設定されていません。実データを取得するには必要です。")
        
        try:
            self._setup_genai(self.api_key)
            # Model name can be different based on the available versions
            # Try the newest model first, then fallback to others
            try:
                self.client = genai.GenerativeModel("gemini-2.5-flash-preview-04-17")
                print("Gemini APIに接続しました: gemini-2.5-flash-preview-04-17")
            except Exception as e1:
                print(f"gemini-2.5-flash-preview-04-17 に接続できませんでした: {e1}")
                try:
                    self.client = genai.GenerativeModel("gemini-1.5-pro")
                    print("Gemini APIに接続しました: gemini-1.5-pro")
                except Exception:
                    try:
                        self.client = genai.GenerativeModel("gemini-1.0-pro")
                        print("Gemini APIに接続しました: gemini-1.0-pro")
                    except Exception:
                        # Fallback to original name
                        self.client = genai.GenerativeModel("gemini-pro")
                        print("Gemini APIに接続しました: gemini-pro")
        except Exception as e:
            raise ConnectionError(f"Gemini API接続エラー: {str(e)}")

    def _setup_genai(self, api_key):
        """
        Set up Google Generative AI with API key.
        
        Args:
            api_key: Google AI API key
        """
        genai.configure(api_key=api_key)

    def analyze_product_description(self, description):
        """
        Analyze a product description to extract features.
        
        Args:
            description: Product description text
            
        Returns:
            dict: Analysis results
        """
        prompt = f"""
        以下の製品説明を分析して、主な特徴、キーワード、品質指標、注意点を抽出してください。
        
        製品説明:
        {description}
        
        JSON形式で以下の情報を返してください:
        1. keywords: 重要なキーワードの配列
        2. main_features: 主な特徴の配列
        3. quality_indicators: 品質を示す指標の配列
        4. warning_signs: 潜在的な問題や注意点の配列
        """
        
        response = self.client.generate_content(prompt)
        result = response.text
        
        # Parse JSON from response
        # Remove markdown code block if present
        result = re.sub(r'```json|```', '', result)
        try:
            analysis = json.loads(result)
        except json.JSONDecodeError:
            print(f"JSON形式ではないレスポンス: {result[:100]}...")
            # Create a basic structure if parsing fails
            analysis = {
                "keywords": [],
                "main_features": [],
                "quality_indicators": [],
                "warning_signs": []
            }
            
            # Try to extract data with regex
            keywords = re.findall(r'"keywords"\s*:\s*\[(.*?)\]', result, re.DOTALL)
            if keywords:
                analysis["keywords"] = [k.strip('" ') for k in keywords[0].split(',')]
        
        return analysis

    def find_common_patterns(self, product_descriptions):
        """
        Find common patterns across multiple product descriptions.
        
        Args:
            product_descriptions: List of product descriptions
            
        Returns:
            dict: Common patterns
        """
        # Join descriptions with separator (limit to 10 to avoid token limits)
        combined_descriptions = "\n===\n".join(product_descriptions[:10])  
        
        prompt = f"""
        以下の複数の製品説明を分析して、共通のパターン、特徴、キーワードを見つけてください。
        これらは私が過去に購入した製品です。私の購入パターンを分析してください。
        
        製品説明:
        {combined_descriptions}
        
        JSON形式で以下の情報を返してください:
        1. common_keywords: 共通するキーワードの配列
        2. preferred_features: 好まれる特徴の配列
        3. avoided_features: 避けるべき特徴の配列
        4. price_sensitivity: 価格に対する感度の説明
        5. quality_preferences: 品質に対する好みの説明
        """
        
        response = self.client.generate_content(prompt)
        result = response.text
        
        # Parse JSON from response
        # Remove markdown code block if present
        result = re.sub(r'```json|```', '', result)
        try:
            patterns = json.loads(result)
        except json.JSONDecodeError:
            print(f"JSON形式ではないレスポンス: {result[:100]}...")
            # Create basic pattern structure if parsing fails
            patterns = {
                "common_keywords": [],
                "preferred_features": [],
                "avoided_features": [],
                "price_sensitivity": "不明",
                "quality_preferences": "不明"
            }
            
            # Try to extract with regex
            keywords = re.findall(r'"common_keywords"\s*:\s*\[(.*?)\]', result, re.DOTALL)
            if keywords:
                patterns["common_keywords"] = [k.strip('" ') for k in keywords[0].split(',')]
        
        return patterns

    def evaluate_product(self, product, common_patterns):
        """
        Evaluate a product against common patterns.
        
        Args:
            product: Product data
            common_patterns: Common patterns data
            
        Returns:
            dict: Evaluation results
        """
        # Create a prompt for evaluation
        product_name = product.get("name", "")
        product_price = product.get("price", 0)
        product_description = product.get("description", "")
        
        # Format patterns for prompt
        preferred_features = ", ".join(common_patterns.get("preferred_features", []))
        avoided_features = ", ".join(common_patterns.get("avoided_features", []))
        price_sensitivity = common_patterns.get("price_sensitivity", "")
        quality_preferences = common_patterns.get("quality_preferences", "")
        
        prompt = f"""
        あなたは市場調査専門家です。私が購入した商品の分析から得た共通パターンに基づいて、以下の商品を評価してください。
        
        共通パターン:
        * 好みの特徴: {preferred_features}
        * 避ける特徴: {avoided_features}
        * 価格感度: {price_sensitivity}
        * 品質の好み: {quality_preferences}
        
        商品詳細:
        * 名称: {product_name}
        * 価格: ￥{product_price:,}
        * 説明: {product_description}
        
        JSON形式で以下の情報を返してください:
        1. recommendation: 「buy」または「skip」
        2. score: 0から1のスコア（0は購入しない、1は必ず購入する）
        3. reasoning: 評価の理由
        4. matching_patterns: 一致した好みのパターンの配列
        5. concerns: 潜在的な懸念事項の配列
        """
        
        response = self.client.generate_content(prompt)
        result = response.text
        
        # Parse JSON from response
        # Remove markdown code block if present
        result = re.sub(r'```json|```', '', result)
        try:
            evaluation = json.loads(result)
        except json.JSONDecodeError:
            print(f"JSON形式ではないレスポンス: {result[:100]}...")
            # Create basic evaluation if parsing fails
            evaluation = {
                "recommendation": "skip",
                "score": 0.5,
                "reasoning": "データ解析エラーのため、正確な評価ができませんでした。",
                "matching_patterns": [],
                "concerns": ["データ解析エラー"]
            }
        
        return evaluation


# Singleton instance
gemini_service = GeminiService()
