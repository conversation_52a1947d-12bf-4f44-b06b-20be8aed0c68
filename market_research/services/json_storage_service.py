"""
JSON storage service for market research data.
"""

import json
import os
from datetime import datetime
from pathlib import Path


class JSONStorageService:
    """
    Service for storing and retrieving data from JSON files.
    """

    def load_data(self, file_path):
        """
        Load data from a JSON file.
        
        Args:
            file_path: Path to the JSON file
            
        Returns:
            List or dict containing the loaded data
        """
        try:
            if not os.path.exists(file_path):
                # Create parent directories if they don't exist
                Path(file_path).parent.mkdir(parents=True, exist_ok=True)
                
                # Create empty file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                return []
                
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"エラーが発生しました：{str(e)}")
            return []

    def save_data(self, data, file_path):
        """
        Save data to a JSON file.
        
        Args:
            data: Data to save
            file_path: Path to the JSON file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create parent directories if they don't exist
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"エラーが発生しました：{str(e)}")
            return False

    def append_data(self, item, file_path):
        """
        Append an item to a JSON array file.
        
        Args:
            item: Item to append
            file_path: Path to the JSON file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            data = self.load_data(file_path)
            
            # Add timestamp if not present
            if isinstance(item, dict) and 'created_at' not in item:
                item['created_at'] = datetime.now().isoformat()
            
            # Check if item with same ID already exists
            if isinstance(item, dict) and 'id' in item:
                for i, existing_item in enumerate(data):
                    if isinstance(existing_item, dict) and existing_item.get('id') == item['id']:
                        # Update existing item
                        data[i] = item
                        self.save_data(data, file_path)
                        return True
            
            # Append new item
            data.append(item)
            self.save_data(data, file_path)
            return True
        except Exception as e:
            print(f"エラーが発生しました：{str(e)}")
            return False

    def get_by_id(self, id_value, file_path):
        """
        Get an item by its ID.
        
        Args:
            id_value: ID value to search for
            file_path: Path to the JSON file
            
        Returns:
            dict: Item with the specified ID, or None if not found
        """
        try:
            data = self.load_data(file_path)
            for item in data:
                if isinstance(item, dict) and item.get('id') == id_value:
                    return item
            return None
        except Exception as e:
            print(f"エラーが発生しました：{str(e)}")
            return None

    def delete_by_id(self, id_value, file_path):
        """
        Delete an item by its ID.
        
        Args:
            id_value: ID value to delete
            file_path: Path to the JSON file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            data = self.load_data(file_path)
            data = [item for item in data if not (isinstance(item, dict) and item.get('id') == id_value)]
            self.save_data(data, file_path)
            return True
        except Exception as e:
            print(f"エラーが発生しました：{str(e)}")
            return False


# Singleton instance
json_storage = JSONStorageService()
