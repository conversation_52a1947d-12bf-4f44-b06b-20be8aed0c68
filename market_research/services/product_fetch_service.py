"""
Service for fetching product data from Yahoo Auctions.
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import random
from urllib.parse import urljoin, quote

from ..config import YAHOO_AUCTION_BASE_URL


class ProductFetchService:
    """
    Service for fetching product data from Yahoo Auctions.
    """

    def __init__(self):
        """
        Initialize the product fetch service.
        """
        self.base_url = YAHOO_AUCTION_BASE_URL
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def _get_with_retry(self, url, max_retries=3, retry_delay=1):
        """
        Get a URL with retry logic.
        
        Args:
            url: URL to get
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
            
        Returns:
            Response object or None if failed
        """
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                print(f"リクエストエラー ({attempt+1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
        return None

    def get_product_details(self, auction_id):
        """
        Get detailed information about a specific auction.
        
        Args:
            auction_id: Yahoo Auction ID
            
        Returns:
            dict: Product details
        """
        url = f"{self.base_url}/auction/{auction_id}"
        response = self._get_with_retry(url)
        
        if not response:
            return {"error": f"Failed to fetch auction {auction_id}"}
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Initialize product details
        product = {
            "id": auction_id,
            "url": url
        }
        
        # Extract product name
        title_elem = soup.select_one('.ProductTitle__title')
        if title_elem:
            product["name"] = title_elem.text.strip()
        else:
            # Try alternative selector
            title_elem = soup.select_one('h1.ProductTitle__text')
            if title_elem:
                product["name"] = title_elem.text.strip()
        
        # Extract price
        price_elem = soup.select_one('.Price__value')
        if price_elem:
            price_text = price_elem.text.strip().replace(',', '').replace('円', '')
            try:
                product["price"] = int(price_text)
            except ValueError:
                product["price"] = 0
        
        # Extract description
        description_elem = soup.select_one('#itm_description')
        if description_elem:
            product["description"] = description_elem.text.strip()
        else:
            # Try alternative selector
            description_elem = soup.select_one('.ProductExplanation__commentArea')
            if description_elem:
                product["description"] = description_elem.text.strip()
        
        # Extract seller information
        seller_elem = soup.select_one('.SellerInfo__name')
        if seller_elem:
            product["seller"] = seller_elem.text.strip()
        
        # Extract images
        product["images"] = []
        image_elems = soup.select('.ProductImage__images img')
        for img in image_elems:
            if 'src' in img.attrs:
                product["images"].append(img['src'])
        
        # Extract condition
        condition_elem = soup.select_one('.ProductTable__item:contains("商品の状態") + .ProductTable__item')
        if condition_elem:
            product["condition"] = condition_elem.text.strip()
        
        # Extract auction end time
        end_time_elem = soup.select_one('.ProductDetail__item:contains("終了日時") + .ProductDetail__item')
        if end_time_elem:
            product["end_time"] = end_time_elem.text.strip()
        
        return product

    def search_products(self, query, category=None, min_price=None, max_price=None, page=1):
        """
        Search for products on Yahoo Auctions.
        
        Args:
            query: Search query
            category: Category ID (optional)
            min_price: Minimum price (optional)
            max_price: Maximum price (optional)
            page: Page number (optional)
            
        Returns:
            list: List of product search results
        """
        # Build search URL
        params = {
            'p': query,
            'va': query,
            'exflg': '1',  # Use exact search
            'b': (page - 1) * 20 + 1,  # Page offset
            'n': '20',  # Results per page
        }
        
        # Add optional parameters
        if category:
            params['auccat'] = category
        if min_price:
            params['min'] = min_price
        if max_price:
            params['max'] = max_price
        
        # Build query string
        query_str = '&'.join([f"{k}={quote(str(v))}" for k, v in params.items()])
        url = f"{self.base_url}/search/search?{query_str}"
        
        response = self._get_with_retry(url)
        if not response:
            return {"error": "Failed to search products", "results": []}
            
        soup = BeautifulSoup(response.text, 'html.parser')
        results = []
        
        # Extract search results
        items = soup.select('.Product')
        for item in items:
            try:
                # Extract auction ID from URL
                link_elem = item.select_one('a.Product__imageLink')
                if not link_elem or 'href' not in link_elem.attrs:
                    continue
                    
                auction_url = link_elem['href']
                auction_id_match = re.search(r'/auction/([a-zA-Z0-9]+)', auction_url)
                if not auction_id_match:
                    continue
                    
                auction_id = auction_id_match.group(1)
                
                # Extract title
                title_elem = item.select_one('.Product__title')
                title = title_elem.text.strip() if title_elem else ""
                
                # Extract price
                price_elem = item.select_one('.Product__priceValue')
                price = 0
                if price_elem:
                    price_text = price_elem.text.strip().replace(',', '').replace('円', '')
                    try:
                        price = int(price_text)
                    except ValueError:
                        price = 0
                
                # Extract image
                img_elem = item.select_one('.Product__imageData')
                img_url = img_elem['src'] if img_elem and 'src' in img_elem.attrs else ""
                
                # Add to results
                results.append({
                    "id": auction_id,
                    "name": title,
                    "price": price,
                    "url": auction_url,
                    "image": img_url
                })
            except Exception as e:
                print(f"結果解析エラー: {str(e)}")
                continue
        
        return {
            "results": results,
            "total": len(results)
        }

    def get_won_auctions_from_database(self, database_service, limit=100):
        """
        Get won auctions from the database.
        
        Args:
            database_service: Database service
            limit: Maximum number of auctions to retrieve
            
        Returns:
            list: List of won auctions
        """
        # This is a placeholder for now - in the real implementation,
        # this would query the Supabase database for won auctions
        # For now, we'll return an empty list
        return []


# Singleton instance
product_fetch_service = ProductFetchService()
