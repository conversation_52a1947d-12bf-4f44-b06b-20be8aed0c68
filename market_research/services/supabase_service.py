"""
Service for interacting with Supabase.
"""

import os
import json
import sys
from pathlib import Path
from supabase import create_client, Client
from dotenv import load_dotenv

# Thử import vớ<PERSON> cả absolute và relative path
try:
    # Relative imports
    from .yahoo_auction_service import yahoo_auction_service
except ImportError:
    try:
        # Absolute imports từ package
        from market_research.services.yahoo_auction_service import yahoo_auction_service
    except ImportError:
        # Direct imports
        from services.yahoo_auction_service import yahoo_auction_service

# Try to load from .env file in the project root
try:
    # Find project root (parent of market_research directory)
    current_dir = Path(__file__).resolve().parent  # services directory
    market_research_dir = current_dir.parent  # market_research directory
    project_root = market_research_dir.parent  # project root directory
    
    # Load .env file from project root
    env_path = project_root / ".env"
    if env_path.exists():
        load_dotenv(dotenv_path=str(env_path))
        print(f"Loaded environment variables from {env_path}")
    else:
        print(f"Warning: .env file not found at {env_path}")
    
    # Get Supabase credentials from environment
    SUPABASE_URL = os.environ.get("SUPABASE_URL")
    SUPABASE_KEY = os.environ.get("SUPABASE_KEY")
    
    if SUPABASE_URL and SUPABASE_KEY:
        print("Supabase credentials loaded from .env file")
    else:
        print("Warning: SUPABASE_URL or SUPABASE_KEY not found in environment variables")

except Exception as e:
    print(f"Error loading environment variables: {str(e)}")
    SUPABASE_URL = None
    SUPABASE_KEY = None


class SupabaseService:
    """
    Service for fetching data from Supabase.
    """
    
    def __init__(self):
        """
        Initialize the Supabase service.
        """
        self.url = SUPABASE_URL
        self.key = SUPABASE_KEY
        
        if not self.url or not self.key:
            raise ValueError("SUPABASE_URL と SUPABASE_KEY 環境変数が設定されていません。")
        
        try:
            print(f"Connecting to Supabase at URL: {self.url}")
            self.client = create_client(self.url, self.key)
            print("Supabaseに接続しました")
            
            # Test connection by fetching a small amount of data
            try:
                test_response = self.client.table('auctions').select('id').limit(1).execute()
                if hasattr(test_response, 'data'):
                    print(f"Test query succeeded: Found {len(test_response.data)} records")
                else:
                    print("Test query returned no data")
            except Exception as e:
                print(f"Test query failed: {str(e)}")
                
        except Exception as e:
            raise ConnectionError(f"Supabase接続エラー: {str(e)}")
    
    def get_products(self, limit=30):
        """
        Get products from auctions table in Supabase.
        
        Args:
            limit: Maximum number of products to retrieve
            
        Returns:
            list: List of products
        """
        try:
            response = self.client.table('auctions').select('*').limit(limit).execute()
            
            if hasattr(response, 'data'):
                products = response.data
                print(f"Supabaseから{len(products)}件の商品データを取得しました")
                
                # Convert to appropriate format for analysis
                formatted_products = []
                for product in products:
                    formatted_product = {
                        "id": product.get("id", ""),
                        "title": product.get("title", ""),
                        "description": product.get("description", ""),
                        "price": product.get("price", 0),
                        "seller": product.get("seller_id", ""),
                        "condition": product.get("condition", ""),
                        "end_time": product.get("end_time", ""),
                        "image_url": product.get("image_url", ""),
                    }
                    formatted_products.append(formatted_product)
                
                return formatted_products
            else:
                print("データが見つかりませんでした")
                return []
                
        except Exception as e:
            print(f"Supabaseからのデータ取得エラー: {str(e)}")
            return []
    
    def get_purchase_history(self, limit=50):
        """
        Lấy lịch sử mua hàng từ Supabase bảng 'auctions'
        
        Args:
            limit: Số lượng tối đa các mục cần lấy
            
        Returns:
            list: Danh sách các mục lịch sử mua hàng đã được định dạng
        """
        if not self.client:
            print("Supabaseに接続できません")
            return []
            
        try:
            # Truy vấn trực tiếp bảng auctions với các trạng thái phù hợp
            response = self.client.table('auctions').select('*') \
                .eq("status", "finished") \
                .limit(limit).execute()
            
            if hasattr(response, 'data') and response.data:
                purchase_data = response.data
                print(f"Supabaseから{len(purchase_data)}件の完了済みオークションを取得しました")
                
                # Chuyển đổi dữ liệu sang định dạng phù hợp với market_research
                formatted_history = []
                for item in purchase_data:
                    # Lấy thông tin chi tiết từ Yahoo Auction nếu có auction_id
                    auction_id = item.get('auction_id')
                    description = f"{item.get('name', '')}\n"
                    auction_url = ""
                    
                    if auction_id:
                        try:
                            # Tạo URL từ auction_id
                            auction_url = f"https://auctions.yahoo.co.jp/jp/auction/{auction_id}"
                            # Lấy thông tin chi tiết từ Yahoo Auction
                            product_details = yahoo_auction_service.get_product_details(auction_id)
                            
                            if product_details and product_details.get('description'):
                                # Sử dụng mô tả chi tiết từ Yahoo Auction
                                description = product_details.get('description')
                        except Exception as e:
                            print(f"Không thể lấy thông tin chi tiết cho auction_id {auction_id}: {str(e)}")
                    
                    # Bổ sung thông tin giao hàng và thanh toán nếu có
                    if item.get('delivery_method'):
                        description += f"配送方法: {item.get('delivery_method')}\n"
                    if item.get('payment_method'):
                        description += f"支払方法: {item.get('payment_method')}\n"
                        
                    # Tạo đối tượng sản phẩm theo định dạng mong muốn
                    product = {
                        "id": item.get("id") or auction_id,  # Sử dụng auction_id nếu id không có
                        "name": item.get("name"),
                        "price": item.get("price"),
                        "description": description,
                        "seller": item.get("buy_from", ""),
                        "purchase_date": item.get("auction_end_time"),
                        "url": auction_url or item.get("url", ""),  # Ưu tiên auction_url
                        "auction_id": auction_id  # Thêm auction_id vào sản phẩm
                    }
                    formatted_history.append(product)
                
                return formatted_history
            else:
                print("購入履歴が見つかりませんでした")
                return []
                
        except Exception as e:
            print(f"購入履歴の取得エラー: {str(e)}")
            return []


# Create an instance of the service
supabase_service = SupabaseService()
