"""
Service for interacting with Yahoo Auctions.
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random


class YahooAuctionService:
    """
    Service for fetching data from Yahoo Auctions.
    """
    
    def __init__(self):
        """
        Initialize the Yahoo Auction service.
        """
        self.base_url = "https://auctions.yahoo.co.jp"
        self.search_url = f"{self.base_url}/search/search"
        self.item_url = f"{self.base_url}/auction"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
    
    def search_products(self, keyword, max_results=2):
        """
        Search for products on Yahoo Auctions.
        
        Args:
            keyword: Search keyword
            max_results: Maximum number of results to return
            
        Returns:
            list: List of product IDs
        """
        print(f"[DEBUG] Bắt đầu tìm kiếm sản phẩm với từ khóa: {keyword}")
        try:
            # Chỉ sử dụng từ khóa ngắn, cắt bỏ nếu quá dài
            if len(keyword) > 30:
                # Nếu từ khóa dài, chỉ lấy 2 từ đầu tiên
                words = keyword.split()
                if len(words) > 2:
                    keyword = " ".join(words[:2])
                else:
                    # Hoặc cắt bỏ nếu là một cụm từ dài
                    keyword = keyword[:30]
            
            print(f"「{keyword}」で検索中...")
            
            # Thêm một số tham số để cải thiện kết quả tìm kiếm
            params = {
                "p": keyword,  # từ khóa tìm kiếm
                "va": keyword,  # giá trị chính xác
                "exflg": "1",  # chỉ hiển thị tìm kiếm chính xác
                "b": "1",      # trang đầu tiên
                "n": str(max_results),  # số kết quả trên một trang
                "mode": "2",   # chế độ hiển thị danh sách
                "s1": "cbids",  # sắp xếp theo số lượng đấu giá
                "o1": "d"      # sắp xếp giảm dần
            }
            
            print(f"[DEBUG] Gửi yêu cầu đến: {self.search_url} với params: {params}")
            response = requests.get(self.search_url, params=params, headers=self.headers, timeout=10)
            print(f"[DEBUG] Nhận được phản hồi với mã trạng thái: {response.status_code}")
            response.raise_for_status()
            
            # Lưu HTML để debug nếu cần
            # with open(f"search_{keyword}.html", "w", encoding="utf-8") as f:
            #     f.write(response.text)
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Kiểm tra cả li.Product__item và .ProductList li (tùy ứng với cấu trúc trang khác nhau)
            items = soup.select("li.Product__item") or soup.select(".ProductList li") or soup.select(".Result__item")
            
            # Nếu vẫn không tìm thấy, thử tìm kiếm bất kỳ li nào chứa link đến đấu giá
            if not items:
                all_links = soup.select("a[href*='/auction/']")  # tất cả các link chứa '/auction/'
                product_ids = []
                for link in all_links:
                    href = link.get("href", "")
                    match = re.search(r"auction/([a-zA-Z0-9]+)", href)
                    if match:
                        product_ids.append(match.group(1))
                
                print(f"「{keyword}」で{len(product_ids)}件の結果が見つかりました")
                return list(set(product_ids))[:max_results]  # Lọc trùng
            
            product_ids = []
            for item in items:
                # Extract product ID from data-auction-id or from link
                if item.get("data-auction-id"):
                    product_ids.append(item.get("data-auction-id"))
                else:
                    link = item.select_one("a")
                    if link and link.get("href"):
                        # Extract ID from URL like https://page.auctions.yahoo.co.jp/jp/auction/x1234567
                        match = re.search(r"auction/([a-zA-Z0-9]+)", link.get("href"))
                        if match:
                            product_ids.append(match.group(1))
            
            print(f"「{keyword}」で{len(product_ids)}件の結果が見つかりました")
            return product_ids[:max_results]
            
        except Exception as e:
            print(f"検索エラー: {str(e)}")
            return []
    
    def get_product_details(self, product_id):
        """
        Get details for a specific product.
        
        Args:
            product_id: Yahoo Auction product ID
            
        Returns:
            dict: Product details
        """
        try:
            print(f"商品情報を取得中: {product_id}")
            
            # Add a small delay to avoid rate limiting
            time.sleep(random.uniform(0.5, 2.0))
            
            url = f"{self.item_url}/{product_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract product details
            product = {
                "id": product_id,
                "url": url
            }
            
            # Extract name
            name_tag = soup.select_one("h1.ProductTitle__text")
            if name_tag:
                product["name"] = name_tag.text.strip()
            
            # Extract price
            price_tag = soup.select_one("span.Price__value")
            if price_tag:
                price_text = price_tag.text.strip().replace(",", "").replace("円", "")
                try:
                    product["price"] = int(price_text)
                except ValueError:
                    product["price"] = 0
            
            # Extract description
            description_tag = soup.select_one("div.ProductExplanation__commentBody, div.ProductExplanation__comments")
            if description_tag:
                product["description"] = description_tag.text.strip()
            
            # Extract seller
            seller_tag = soup.select_one("span.Seller__name a")
            if seller_tag:
                product["seller"] = seller_tag.text.strip()
            
            # Extract condition
            condition_tag = soup.select_one("div.ProductDetail__item:contains('状態') span.ProductDetail__item--text, dt:contains('商品の状態') + dd")
            if condition_tag:
                product["condition"] = condition_tag.text.strip()
            
            # Extract remaining time
            time_tag = soup.select_one("dt:contains('残り時間') + dd, div.ProductDetail__item:contains('残り時間') span.ProductDetail__item--text")
            if time_tag:
                product["remaining_time"] = time_tag.text.strip()
            
            # Extract bid count
            bid_tag = soup.select_one("dt:contains('入札件数') + dd, div.ProductDetail__item:contains('入札件数') span.ProductDetail__item--text")
            if bid_tag:
                try:
                    product["bid_count"] = int(bid_tag.text.strip().replace("件", ""))
                except ValueError:
                    product["bid_count"] = 0
            
            # Extract shipping info
            shipping_tag = soup.select_one("dt:contains('送料負担') + dd, div.ProductDetail__item:contains('送料負担') span.ProductDetail__item--text")
            if shipping_tag:
                product["shipping_fee"] = "送料込み" if "落札者" in shipping_tag.text else "着払い"
                
            # Extract location
            location_tag = soup.select_one("dt:contains('発送元地域') + dd, div.ProductDetail__item:contains('発送元地域') span.ProductDetail__item--text")
            if location_tag:
                product["location"] = location_tag.text.strip()
            
            # Extract images
            image_tags = soup.select("div.ProductImage__inner img")
            if image_tags:
                product["images"] = [img.get("src") for img in image_tags if img.get("src")]
            
            return product
            
        except Exception as e:
            print(f"商品情報取得エラー: {str(e)}")
            return {
                "id": product_id,
                "url": f"{self.item_url}/{product_id}",
                "name": "情報取得エラー",
                "price": 0,
                "description": "商品情報の取得中にエラーが発生しました。"
            }
    
    def get_purchase_history(self, limit=20):
        """
        Get recently sold products to simulate purchase history.
        Since we can't access actual purchase history without login,
        we'll search for completed auctions instead.
        
        Args:
            limit: Maximum number of history items to return
            
        Returns:
            list: Purchase history data (recent completed auctions)
        """
        print("最近の取引データを取得中...")
        

        
        # Search for products with each keyword
        purchase_history = []
        
        for keyword in popular_keywords:
            # Get product IDs from search
            product_ids = self.search_products(keyword, max_results=5)  # Limit to 5 per keyword
            
            # Get details for each product
            for product_id in product_ids:
                if len(purchase_history) >= limit:
                    break
                    
                product_details = self.get_product_details(product_id)
                
                # Add fake purchase date since we don't have real one
                import datetime
                import random
                
                # Random date in the last 30 days
                days_ago = random.randint(1, 30)
                purchase_date = (datetime.datetime.now() - datetime.timedelta(days=days_ago)).strftime("%Y-%m-%d")
                product_details["purchase_date"] = purchase_date
                
                purchase_history.append(product_details)
                
                # Add a small delay between requests
                time.sleep(random.uniform(0.5, 1.0))
            
            if len(purchase_history) >= limit:
                break
        
        print(f"最近の取引データ: {len(purchase_history)}件取得しました")
        return purchase_history


# Singleton instance
yahoo_auction_service = YahooAuctionService()
