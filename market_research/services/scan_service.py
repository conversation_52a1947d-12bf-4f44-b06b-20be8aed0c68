"""
Service for scanning Yahoo Auctions for new products.
"""

import time
import datetime
import random
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed

from ..config import (
    SCANNED_PRODUCTS_FILE,
    COMMON_PATTERNS_FILE,
    RECOMMENDATIONS_FILE,
    MAX_PRODUCTS_TO_SCAN
)
from .json_storage_service import json_storage
from .product_fetch_service import product_fetch_service
from .gemini_service import gemini_service


class ScanService:
    """
    Service for scanning Yahoo Auctions for new products.
    """

    def __init__(self):
        """
        Initialize the scan service.
        """
        self.max_products = MAX_PRODUCTS_TO_SCAN

    def generate_search_queries(self, common_patterns):
        """
        Generate search queries based on common patterns.
        
        Args:
            common_patterns: Common patterns data
            
        Returns:
            list: List of search queries
        """
        queries = []
        
        # Use preferred keywords as search queries
        if 'preferred_keywords' in common_patterns:
            for keyword in common_patterns['preferred_keywords']:
                queries.append(keyword)
        
        # Use categories as search queries
        if 'categories' in common_patterns:
            for category in common_patterns['categories']:
                queries.append(category)
                
        # If no queries were generated, use some default ones
        if not queries:
            queries = ["中古", "美品", "動作確認済み"]
            
        return queries

    def scan_new_products(self):
        """
        Scan Yahoo Auctions for new products based on common patterns.
        
        Returns:
            dict: Scan results
        """
        # Load common patterns
        common_patterns = json_storage.load_data(COMMON_PATTERNS_FILE)
        if not common_patterns or len(common_patterns) == 0:
            print("警告: 共通パターンが見つかりません。デフォルトパターンを使用します。")
            common_patterns = [{
                "id": str(uuid.uuid4()),
                "preferred_keywords": ["中古", "美品", "動作確認済み"],
                "avoided_keywords": ["ジャンク", "故障"],
                "price_range": {"min": 1000, "max": 50000},
                "categories": ["電子機器", "コレクション"],
                "quality_indicators": ["動作確認済み", "付属品あり"]
            }]
            json_storage.save_data(common_patterns, COMMON_PATTERNS_FILE)
        
        # Get the first common pattern
        pattern = common_patterns[0] if common_patterns else {}
        
        # Generate search queries
        queries = self.generate_search_queries(pattern)
        
        # Get price range
        price_range = pattern.get('price_range', {})
        min_price = price_range.get('min', None)
        max_price = price_range.get('max', None)
        
        # Load already scanned products to avoid duplicates
        scanned_products = json_storage.load_data(SCANNED_PRODUCTS_FILE)
        scanned_ids = set(item.get('id') for item in scanned_products if isinstance(item, dict))
        
        # Scan for new products
        new_products = []
        total_scanned = 0
        
        for query in queries:
            if total_scanned >= self.max_products:
                break
                
            print(f"「{query}」で検索中...")
            search_results = product_fetch_service.search_products(
                query=query,
                min_price=min_price,
                max_price=max_price,
                page=1
            )
            
            if 'error' in search_results:
                print(f"検索エラー: {search_results['error']}")
                continue
                
            results = search_results.get('results', [])
            print(f"「{query}」で{len(results)}件の結果が見つかりました")
            
            for product in results:
                if total_scanned >= self.max_products:
                    break
                    
                product_id = product.get('id')
                
                # Skip if already scanned
                if product_id in scanned_ids:
                    continue
                    
                # Get detailed product information
                print(f"商品情報を取得中: {product_id}")
                detailed_product = product_fetch_service.get_product_details(product_id)
                
                if 'error' in detailed_product:
                    print(f"商品取得エラー: {detailed_product['error']}")
                    continue
                
                # Add to new products
                new_products.append(detailed_product)
                scanned_ids.add(product_id)
                
                # Save to scanned products
                json_storage.append_data(detailed_product, SCANNED_PRODUCTS_FILE)
                
                total_scanned += 1
                
                # Random delay to avoid overloading the server
                time.sleep(random.uniform(1, 3))
        
        return {
            "total_scanned": total_scanned,
            "new_products": new_products
        }

    def evaluate_products(self):
        """
        Evaluate scanned products and generate recommendations.
        
        Returns:
            dict: Evaluation results
        """
        # Load common patterns
        common_patterns = json_storage.load_data(COMMON_PATTERNS_FILE)
        if not common_patterns or len(common_patterns) == 0:
            print("警告: 共通パターンが見つかりません。評価をスキップします。")
            return {"error": "No common patterns found"}
        
        # Get the first common pattern
        pattern = common_patterns[0] if common_patterns else {}
        
        # Load scanned products
        scanned_products = json_storage.load_data(SCANNED_PRODUCTS_FILE)
        
        # Load existing recommendations to avoid duplicates
        recommendations = json_storage.load_data(RECOMMENDATIONS_FILE)
        recommended_ids = set(item.get('product_id') for item in recommendations if isinstance(item, dict))
        
        # Products to evaluate
        evaluation_products = [p for p in scanned_products if p.get('id') not in recommended_ids]
        
        if not evaluation_products:
            print("評価する新商品がありません。")
            return {"total_evaluated": 0, "recommendations": []}
        
        print(f"{len(evaluation_products)}件の商品を評価中...")
        
        # Evaluate products
        new_recommendations = []
        
        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_product = {
                executor.submit(self._evaluate_single_product, product, pattern): product 
                for product in evaluation_products[:50]  # Limit to 50 products
            }
            
            for future in as_completed(future_to_product):
                product = future_to_product[future]
                try:
                    evaluation = future.result()
                    
                    recommendation = {
                        "id": str(uuid.uuid4()),
                        "created_at": datetime.datetime.now().isoformat(),
                        "product_id": product.get('id'),
                        "product_name": product.get('name'),
                        "product_price": product.get('price'),
                        "product_url": product.get('url'),
                        "score": evaluation.get('score', 0),
                        "recommendation": evaluation.get('recommendation', 'skip'),
                        "reasoning": evaluation.get('reasoning', ''),
                        "highlights": evaluation.get('highlights', []),
                        "concerns": evaluation.get('concerns', [])
                    }
                    
                    # Save recommendation
                    json_storage.append_data(recommendation, RECOMMENDATIONS_FILE)
                    
                    # Add to new recommendations
                    new_recommendations.append(recommendation)
                    
                except Exception as e:
                    print(f"評価エラー: {str(e)}")
        
        return {
            "total_evaluated": len(new_recommendations),
            "recommendations": new_recommendations
        }

    def _evaluate_single_product(self, product, pattern):
        """
        Evaluate a single product using Gemini.
        
        Args:
            product: Product data
            pattern: Common pattern data
            
        Returns:
            dict: Evaluation results
        """
        try:
            return gemini_service.evaluate_product(product, pattern)
        except Exception as e:
            print(f"商品評価エラー: {str(e)}")
            return {
                "score": 0,
                "recommendation": "skip",
                "reasoning": f"評価エラー: {str(e)}",
                "highlights": [],
                "concerns": []
            }


# Singleton instance
scan_service = ScanService()
