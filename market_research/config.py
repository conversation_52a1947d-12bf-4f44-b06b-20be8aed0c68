"""
Configuration for the market research module.
"""

import os
from pathlib import Path

# Base directory for the project
BASE_DIR = Path(__file__).parent

# Data directory
DATA_DIR = BASE_DIR / "data"

# Path to data files
PRODUCT_CATEGORIES_FILE = DATA_DIR / "product_categories.json"
COMMON_PATTERNS_FILE = DATA_DIR / "common_patterns.json"
KEYWORDS_FILE = DATA_DIR / "keywords.json"
SCANNED_PRODUCTS_FILE = DATA_DIR / "scanned_products.json"
RECOMMENDATIONS_FILE = DATA_DIR / "recommendations.json"

# Yahoo Auction API settings
YAHOO_AUCTION_BASE_URL = "https://auctions.yahoo.co.jp"
YAHOO_AUCTION_API_URL = "https://auctions.yahooapis.jp/AuctionWebService/V2"

# Gemini API settings
GEMINI_API_KEY = os.environ.get("GEMINI_API_KEY", "AIzaSyCoBwVOiPHG-CwA-TdKbtPAPoxHFkXKAok")

# Scanning settings
MAX_PRODUCTS_TO_SCAN = 2
SCAN_INTERVAL_MINUTES = 60
