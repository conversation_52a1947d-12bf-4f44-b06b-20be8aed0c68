"""
Utilities for analyzing text for market research.
"""

import re
from collections import Counter


class TextAnalyzer:
    """
    Utility class for analyzing text.
    """

    @staticmethod
    def extract_keywords(text, min_length=2, max_keywords=10):
        """
        Extract keywords from text.
        
        Args:
            text: Text to extract keywords from
            min_length: Minimum length of keywords
            max_keywords: Maximum number of keywords to extract
            
        Returns:
            list: Extracted keywords
        """
        if not text:
            return []
            
        # Simple keyword extraction based on word frequency
        # In a real implementation, we would use more sophisticated NLP techniques
        
        # Split text into words
        words = re.findall(r'\b\w+\b', text)
        
        # Filter out short words
        words = [w for w in words if len(w) >= min_length]
        
        # Count word frequencies
        word_counts = Counter(words)
        
        # Get most common words
        common_words = word_counts.most_common(max_keywords)
        
        return [word for word, count in common_words]

    @staticmethod
    def calculate_keyword_match_score(text, keywords):
        """
        Calculate keyword match score.
        
        Args:
            text: Text to analyze
            keywords: Keywords to match
            
        Returns:
            float: Match score (0-1)
        """
        if not text or not keywords:
            return 0
            
        # Count matching keywords
        match_count = sum(1 for keyword in keywords if keyword in text)
        
        # Calculate score
        score = match_count / len(keywords) if keywords else 0
        
        return min(1.0, score)

    @staticmethod
    def extract_product_condition(text):
        """
        Extract product condition from text.
        
        Args:
            text: Text to extract condition from
            
        Returns:
            str: Extracted condition or empty string if not found
        """
        if not text:
            return ""
            
        # Look for common condition phrases
        condition_phrases = [
            "新品", "未使用", "美品", "中古", "ジャンク", "動作確認済み",
            "良好", "完動品", "難あり", "状態良好", "傷あり"
        ]
        
        for phrase in condition_phrases:
            if phrase in text:
                return phrase
        
        return ""

    @staticmethod
    def has_negative_indicators(text, negative_keywords):
        """
        Check if text has negative indicators.
        
        Args:
            text: Text to check
            negative_keywords: List of negative keywords
            
        Returns:
            bool: True if negative indicators are found, False otherwise
        """
        if not text or not negative_keywords:
            return False
            
        return any(keyword in text for keyword in negative_keywords)

    @staticmethod
    def extract_shipping_info(text):
        """
        Extract shipping information from text.
        
        Args:
            text: Text to extract shipping info from
            
        Returns:
            dict: Extracted shipping info
        """
        shipping_info = {
            "shipping_method": "",
            "shipping_fee": ""
        }
        
        # Look for shipping method
        shipping_methods = ["ヤマト運輸", "佐川急便", "ゆうパック", "メルカリ便", "レターパック"]
        for method in shipping_methods:
            if method in text:
                shipping_info["shipping_method"] = method
                break
        
        # Look for shipping fee patterns
        shipping_fee_match = re.search(r'送料([\d,]+)円', text)
        if shipping_fee_match:
            shipping_info["shipping_fee"] = shipping_fee_match.group(1)
        
        return shipping_info


# Singleton instance
text_analyzer = TextAnalyzer()
