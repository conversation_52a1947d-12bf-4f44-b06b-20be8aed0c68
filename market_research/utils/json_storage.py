"""
JSON storage utility for market research.
"""

import os
import json
import datetime

class JSONStorage:
    """
    Utility for storing and loading data in JSON format.
    """
    
    def __init__(self, file_path):
        """
        Initialize the JSON storage.
        
        Args:
            file_path (str): Path to the JSON file
        """
        self.file_path = file_path
        self.data_dir = os.path.dirname(file_path)
        
        # Create directory if it doesn't exist
        if not os.path.exists(self.data_dir) and self.data_dir:
            os.makedirs(self.data_dir)
    
    def save_data(self, data):
        """
        Save data to JSON file.
        
        Args:
            data: Data to save
        """
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存エラー: {str(e)}")
            return False
    
    def load_data(self):
        """
        Load data from JSON file.
        
        Returns:
            Data from the file or empty list if file doesn't exist
        """
        if not os.path.exists(self.file_path):
            return []
            
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"読み込みエラー: {str(e)}")
            return []
    
    def file_exists(self):
        """
        Check if the JSON file exists.
        
        Returns:
            bool: True if file exists, False otherwise
        """
        return os.path.exists(self.file_path)
    
    def append_data(self, item):
        """
        Append an item to the existing data.
        
        Args:
            item: Item to append
        """
        data = self.load_data()
        data.append(item)
        return self.save_data(data)

# Default instance for backwards compatibility
json_storage = JSONStorage('data.json')
