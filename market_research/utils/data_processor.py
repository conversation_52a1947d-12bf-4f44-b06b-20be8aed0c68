"""
Utilities for processing data for market research.
"""

import json
import datetime
import re
from pathlib import Path
import os


class DataProcessor:
    """
    Utility class for processing data.
    """

    @staticmethod
    def clean_text(text):
        """
        Clean text by removing unnecessary characters.
        
        Args:
            text: Text to clean
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
            
        # Replace multiple whitespace with a single space
        text = re.sub(r'\s+', ' ', text)
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Remove special characters
        text = re.sub(r'[^\w\s\.\,\-\:\;\(\)\[\]\{\}\!\?]', '', text)
        
        return text.strip()

    @staticmethod
    def extract_price_from_text(text):
        """
        Extract price from text.
        
        Args:
            text: Text containing price
            
        Returns:
            int: Extracted price or 0 if not found
        """
        if not text:
            return 0
            
        # Match price patterns like "¥45,000" or "45,000円"
        price_match = re.search(r'[¥￥]([0-9,.]+)|([0-9,.]+)円', text)
        if price_match:
            # Get the matched group (either group 1 or group 2 will have the value)
            price_str = price_match.group(1) or price_match.group(2)
            # Remove commas and convert to int
            if price_str:
                price_str = price_str.replace(',', '')
                try:
                    return int(float(price_str))
                except ValueError:
                    return 0
        
        # Try another pattern for numbers followed by comma and 3 digits
        price_match = re.search(r'([0-9]+),([0-9]{3})', text)
        if price_match:
            try:
                price_str = price_match.group(1) + price_match.group(2)
                return int(price_str)
            except ValueError:
                return 0
        
        return 0

    @staticmethod
    def format_price(price, currency="JPY"):
        """
        Format price with currency.
        
        Args:
            price: Price to format
            currency: Currency code
            
        Returns:
            str: Formatted price
        """
        if currency == "JPY":
            return f"¥{price:,}"
        elif currency == "USD":
            return f"${price:,.2f}"
        else:
            return f"{price:,} {currency}"

    @staticmethod
    def categorize_product(product, categories):
        """
        Categorize a product based on its name and description.
        
        Args:
            product: Product data
            categories: List of categories
            
        Returns:
            int: Category ID or None if not categorized
        """
        if not product or not categories:
            return None
            
        product_name = product.get('name', '').lower()
        product_desc = product.get('description', '').lower()
        
        # Combine name and description for matching
        product_text = product_name + " " + product_desc
        
        # Find category with most keyword matches
        best_category = None
        best_match_count = 0
        
        for category in categories:
            match_count = 0
            keywords = category.get('keywords', [])
            
            for keyword in keywords:
                if keyword.lower() in product_text:
                    match_count += 1
            
            if match_count > best_match_count:
                best_match_count = match_count
                best_category = category
        
        return best_category.get('id') if best_category else None

    @staticmethod
    def filter_recommendations(recommendations, min_score=0.7, recommendation_type="buy"):
        """
        Filter recommendations based on score and type.
        
        Args:
            recommendations: List of recommendations
            min_score: Minimum score to include
            recommendation_type: Type of recommendation to filter for
            
        Returns:
            list: Filtered recommendations
        """
        return [
            r for r in recommendations 
            if r.get('score', 0) >= min_score and r.get('recommendation') == recommendation_type
        ]

    @staticmethod
    def get_formatted_date():
        """
        Get formatted current date.
        
        Returns:
            str: Formatted date
        """
        now = datetime.datetime.now()
        return now.strftime("%Y-%m-%d")

    @staticmethod
    def get_formatted_datetime():
        """
        Get formatted current datetime.
        
        Returns:
            str: Formatted datetime
        """
        now = datetime.datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")


# Singleton instance
data_processor = DataProcessor()
