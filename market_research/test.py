"""
Test script for Yahoo Auctions market research system.
This script tests the core functionality of the system components.
"""

import os
import sys
import json
import time
from pathlib import Path

# Add parent directory to path to allow importing from modules
parent_dir = Path(__file__).parent.parent
sys.path.append(str(parent_dir))

# Import services and utilities
from market_research.services.json_storage_service import json_storage
from market_research.services.product_fetch_service import product_fetch_service
from market_research.models.product_analyzer import product_analyzer
from market_research.utils.data_processor import data_processor
from market_research.utils.text_analyzer import text_analyzer
from market_research.config import (
    SCANNED_PRODUCTS_FILE,
    COMMON_PATTERNS_FILE,
    RECOMMENDATIONS_FILE
)


def test_json_storage():
    """
    Test JSON storage service.
    """
    print("\n===== Testing JSON Storage Service =====")
    
    # Test data
    test_data = {
        "id": "test123",
        "name": "Test Product",
        "price": 5000
    }
    
    # Create a temporary test file
    test_file = "data/test_storage.json"
    
    try:
        # Test save data
        json_storage.save_data([test_data], test_file)
        print("✓ Save data test passed")
        
        # Test load data
        loaded_data = json_storage.load_data(test_file)
        assert loaded_data and len(loaded_data) == 1
        assert loaded_data[0]["id"] == "test123"
        print("✓ Load data test passed")
        
        # Test append data
        new_data = {
            "id": "test456",
            "name": "Another Test Product",
            "price": 7500
        }
        json_storage.append_data(new_data, test_file)
        
        loaded_data = json_storage.load_data(test_file)
        assert loaded_data and len(loaded_data) == 2
        print("✓ Append data test passed")
        
        # Test get by ID
        item = json_storage.get_by_id("test456", test_file)
        assert item and item["name"] == "Another Test Product"
        print("✓ Get by ID test passed")
        
        # Test delete by ID
        json_storage.delete_by_id("test123", test_file)
        loaded_data = json_storage.load_data(test_file)
        assert loaded_data and len(loaded_data) == 1
        assert loaded_data[0]["id"] == "test456"
        print("✓ Delete by ID test passed")
        
    except AssertionError as e:
        print(f"✗ Test failed: {str(e)}")
    finally:
        # Clean up test file
        try:
            os.remove(Path(__file__).parent / test_file)
        except:
            pass


def test_product_fetch():
    """
    Test product fetch service.
    """
    print("\n===== Testing Product Fetch Service =====")
    
    try:
        # Test search products (with a small limit to avoid excessive requests)
        search_results = product_fetch_service.search_products("Nintendo Switch", limit=2)
        print(f"Found {len(search_results)} products")
        assert len(search_results) > 0
        print("✓ Search products test passed")
        
        # Test product details (using first product from search)
        if search_results:
            product_id = search_results[0].get("id")
            if product_id:
                product_details = product_fetch_service.get_product_details(product_id)
                assert product_details and product_details.get("name")
                print(f"Product name: {product_details.get('name')}")
                print("✓ Get product details test passed")
            else:
                print("✗ Cannot test product details: No product ID found")
        else:
            print("✗ Cannot test product details: No search results")
            
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")


def test_text_analyzer():
    """
    Test text analyzer utility.
    """
    print("\n===== Testing Text Analyzer =====")
    
    sample_text = """
    中古のNintendo Switchです。動作確認済み、付属品完備。
    画面に細かい傷がありますが、プレイに支障はありません。
    送料は1500円です。ヤマト運輸で発送予定。
    """
    
    try:
        # Test keyword extraction
        keywords = text_analyzer.extract_keywords(sample_text)
        print(f"Extracted keywords: {keywords}")
        assert len(keywords) > 0
        print("✓ Extract keywords test passed")
        
        # Test condition extraction
        condition = text_analyzer.extract_product_condition(sample_text)
        print(f"Extracted condition: {condition}")
        assert condition
        print("✓ Extract condition test passed")
        
        # Test shipping info extraction
        shipping_info = text_analyzer.extract_shipping_info(sample_text)
        print(f"Extracted shipping info: {shipping_info}")
        assert shipping_info
        print("✓ Extract shipping info test passed")
        
    except AssertionError as e:
        print(f"✗ Test failed: {str(e)}")


def test_data_processor():
    """
    Test data processor utility.
    """
    print("\n===== Testing Data Processor =====")
    
    sample_text = """<div>中古品 PS5 ¥45,000 送料込み</div>"""
    
    try:
        # Test text cleaning
        cleaned_text = data_processor.clean_text(sample_text)
        print(f"Cleaned text: {cleaned_text}")
        assert "<div>" not in cleaned_text
        print("✓ Clean text test passed")
        
        # Test price extraction
        price = data_processor.extract_price_from_text(sample_text)
        print(f"Extracted price: {price}")
        assert price == 45000
        print("✓ Extract price test passed")
        
        # Test price formatting
        formatted_price = data_processor.format_price(45000)
        print(f"Formatted price: {formatted_price}")
        assert formatted_price == "¥45,000"
        print("✓ Format price test passed")
        
    except AssertionError as e:
        print(f"✗ Test failed: {str(e)}")


def main():
    """
    Main test function.
    """
    print("===== Yahoo Auctions Market Research System Tests =====")
    
    # Run tests
    test_json_storage()
    test_text_analyzer()
    test_data_processor()
    
    # Product fetch test is commented out to avoid making 
    # unnecessary web requests during testing
    # Uncomment to test with live data
    # test_product_fetch()
    
    print("\n===== All Tests Completed =====")


if __name__ == "__main__":
    main()
