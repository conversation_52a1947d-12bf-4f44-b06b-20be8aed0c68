# Yahoo Auctions 市場調査ツール

## 概要

このモジュールは、Yahoo Auctionsでの購入履歴を分析し、市場調査を行うためのツールです。Google Gemini AIを活用して、購入傾向を分析し、新しい商品をスキャンして、購入すべきかどうかを評価します。

## 機能

- **購入履歴分析**: 過去に購入した商品を分析し、共通パターンを特定
- **商品スキャン**: Yahoo Auctionsの新商品を自動的にスキャン
- **商品評価**: Gemini AIを使用して商品を評価し、購入推奨を生成
- **データローカル保存**: すべてのデータをローカルJSONファイルに保存

## 使用方法

### 環境変数の設定

```bash
export GEMINI_API_KEY=your_api_key
```

### コマンドラインオプション

```bash
# 購入履歴を分析
python main.py --analyze

# 新商品をスキャン
python main.py --scan

# スキャンした商品を評価
python main.py --evaluate

# データのサマリーを表示
python main.py --summary

# すべてのステップを実行
python main.py --all
```

## データ構造

データは以下のJSONファイルに保存されます：

- `data/product_categories.json`: 製品カテゴリ
- `data/common_patterns.json`: 共通パターン
- `data/keywords.json`: キーワード
- `data/scanned_products.json`: スキャンされた商品
- `data/recommendations.json`: 推奨事項

## 依存関係

- Python 3.8+
- requests
- beautifulsoup4
- google-generativeai

## 将来の拡張

- Supabaseとの統合
- ユーザーインターフェースの強化
- 自動購入オプション
