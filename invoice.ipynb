{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-04-07T05:54:32.053463Z", "start_time": "2025-04-07T05:54:31.941291Z"}}, "source": ["\n", "from supabase import create_client, Client\n", "\n", "supabase: Client = create_client(\"https://cduauvwekzwtsmwgsngf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdWF1dndla3p3dHNtd2dzbmdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjkzMDk0MjYsImV4cCI6MjA0NDg4NTQyNn0.V6pQ0FRD69bYQ-CgoiKPH9SXNqdXSs-9QYM_UtqjNVw\")\n", "\n", "plan_id = 5"], "outputs": [{"ename": "TypeError", "evalue": "Client.__init__() got an unexpected keyword argument 'proxy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01ms<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m create_client, Client\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m supabase: Client = \u001b[43mcreate_client\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mhttps://cduauvwekzwtsmwgsngf.supabase.co\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43meyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdWF1dndla3p3dHNtd2dzbmdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjkzMDk0MjYsImV4cCI6MjA0NDg4NTQyNn0.V6pQ0FRD69bYQ-CgoiKPH9SXNqdXSs-9QYM_UtqjNVw\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      5\u001b[39m plan_id = \u001b[32m5\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/supabase/_sync/client.py:295\u001b[39m, in \u001b[36mcreate_client\u001b[39m\u001b[34m(supabase_url, supabase_key, options)\u001b[39m\n\u001b[32m    264\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate_client\u001b[39m(\n\u001b[32m    265\u001b[39m     supabase_url: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m    266\u001b[39m     supabase_key: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m    267\u001b[39m     options: ClientOptions = ClientOptions(),\n\u001b[32m    268\u001b[39m ) -> SyncClient:\n\u001b[32m    269\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Create client function to instantiate supabase client like JS runtime.\u001b[39;00m\n\u001b[32m    270\u001b[39m \n\u001b[32m    271\u001b[39m \u001b[33;03m    Parameters\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    293\u001b[39m \u001b[33;03m    Client\u001b[39;00m\n\u001b[32m    294\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m295\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mSyncClient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    296\u001b[39m \u001b[43m        \u001b[49m\u001b[43msupabase_url\u001b[49m\u001b[43m=\u001b[49m\u001b[43msupabase_url\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msupabase_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43msupabase_key\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\n\u001b[32m    297\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/supabase/_sync/client.py:97\u001b[39m, in \u001b[36mSyncClient.create\u001b[39m\u001b[34m(cls, supabase_url, supabase_key, options)\u001b[39m\n\u001b[32m     90\u001b[39m \u001b[38;5;129m@classmethod\u001b[39m\n\u001b[32m     91\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m     92\u001b[39m     \u001b[38;5;28mcls\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     95\u001b[39m     options: ClientOptions = ClientOptions(),\n\u001b[32m     96\u001b[39m ):\n\u001b[32m---> \u001b[39m\u001b[32m97\u001b[39m     client = \u001b[38;5;28;43mcls\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msupabase_url\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msupabase_key\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     98\u001b[39m     client._auth_token = client._get_token_header()\n\u001b[32m     99\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m client\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/supabase/_sync/client.py:75\u001b[39m, in \u001b[36mSyncClient.__init__\u001b[39m\u001b[34m(self, supabase_url, supabase_key, options)\u001b[39m\n\u001b[32m     72\u001b[39m \u001b[38;5;28mself\u001b[39m.schema = options.schema\n\u001b[32m     74\u001b[39m \u001b[38;5;66;03m# Instantiate clients.\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m75\u001b[39m \u001b[38;5;28mself\u001b[39m.auth = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_init_supabase_auth_client\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     76\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth_url\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mauth_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     77\u001b[39m \u001b[43m    \u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     78\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     79\u001b[39m \u001b[38;5;66;03m# TODO: Bring up to parity with JS client.\u001b[39;00m\n\u001b[32m     80\u001b[39m \u001b[38;5;66;03m# self.realtime: SupabaseRealtimeClient = self._init_realtime_client(\u001b[39;00m\n\u001b[32m     81\u001b[39m \u001b[38;5;66;03m#     realtime_url=self.realtime_url,\u001b[39;00m\n\u001b[32m     82\u001b[39m \u001b[38;5;66;03m#     supabase_key=self.supabase_key,\u001b[39;00m\n\u001b[32m     83\u001b[39m \u001b[38;5;66;03m# )\u001b[39;00m\n\u001b[32m     84\u001b[39m \u001b[38;5;28mself\u001b[39m.realtime = \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/supabase/_sync/client.py:217\u001b[39m, in \u001b[36mSyncClient._init_supabase_auth_client\u001b[39m\u001b[34m(auth_url, client_options)\u001b[39m\n\u001b[32m    211\u001b[39m \u001b[38;5;129m@staticmethod\u001b[39m\n\u001b[32m    212\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_init_supabase_auth_client\u001b[39m(\n\u001b[32m    213\u001b[39m     auth_url: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m    214\u001b[39m     client_options: ClientOptions,\n\u001b[32m    215\u001b[39m ) -> SyncSupabaseAuthClient:\n\u001b[32m    216\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Creates a wrapped instance of the GoTrue Client.\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m217\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mSyncSupabaseAuthClient\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    218\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth_url\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    219\u001b[39m \u001b[43m        \u001b[49m\u001b[43mauto_refresh_token\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43mauto_refresh_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    220\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpersist_session\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpersist_session\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    221\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    222\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[43m        \u001b[49m\u001b[43mflow_type\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43mflow_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    224\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/supabase/_sync/auth_client.py:31\u001b[39m, in \u001b[36mSyncSupabaseAuthClient.__init__\u001b[39m\u001b[34m(self, url, headers, storage_key, auto_refresh_token, persist_session, storage, http_client, flow_type)\u001b[39m\n\u001b[32m     28\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m headers \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m     29\u001b[39m     headers = {}\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m \u001b[43mSyncGoTrueClient\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m     32\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     33\u001b[39m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     34\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     35\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     36\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauto_refresh_token\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauto_refresh_token\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     37\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpersist_session\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpersist_session\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     38\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     39\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhttp_client\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhttp_client\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     40\u001b[39m \u001b[43m    \u001b[49m\u001b[43mflow_type\u001b[49m\u001b[43m=\u001b[49m\u001b[43mflow_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/gotrue/_sync/gotrue_client.py:101\u001b[39m, in \u001b[36mSyncGoTrueClient.__init__\u001b[39m\u001b[34m(self, url, headers, storage_key, auto_refresh_token, persist_session, storage, http_client, flow_type, verify, proxy)\u001b[39m\n\u001b[32m     87\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\n\u001b[32m     88\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m     89\u001b[39m     *,\n\u001b[32m   (...)\u001b[39m\u001b[32m     99\u001b[39m     proxy: Optional[\u001b[38;5;28mstr\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    100\u001b[39m ) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[43mSyncGoTrueBaseAPI\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    102\u001b[39m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    103\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43murl\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mGOTRUE_URL\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    104\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mDEFAULT_HEADERS\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    105\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhttp_client\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhttp_client\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    106\u001b[39m \u001b[43m        \u001b[49m\u001b[43mverify\u001b[49m\u001b[43m=\u001b[49m\u001b[43mverify\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    107\u001b[39m \u001b[43m        \u001b[49m\u001b[43mproxy\u001b[49m\u001b[43m=\u001b[49m\u001b[43mproxy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    108\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    109\u001b[39m     \u001b[38;5;28mself\u001b[39m._storage_key = storage_key \u001b[38;5;129;01mor\u001b[39;00m STORAGE_KEY\n\u001b[32m    110\u001b[39m     \u001b[38;5;28mself\u001b[39m._auto_refresh_token = auto_refresh_token\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/gotrue/_sync/gotrue_base_api.py:28\u001b[39m, in \u001b[36mSyncGoTrueBaseAPI.__init__\u001b[39m\u001b[34m(self, url, headers, http_client, verify, proxy)\u001b[39m\n\u001b[32m     26\u001b[39m \u001b[38;5;28mself\u001b[39m._url = url\n\u001b[32m     27\u001b[39m \u001b[38;5;28mself\u001b[39m._headers = headers\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m \u001b[38;5;28mself\u001b[39m._http_client = http_client \u001b[38;5;129;01mor\u001b[39;00m \u001b[43mSyncClient\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     29\u001b[39m \u001b[43m    \u001b[49m\u001b[43mverify\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mbool\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mverify\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     30\u001b[39m \u001b[43m    \u001b[49m\u001b[43mproxy\u001b[49m\u001b[43m=\u001b[49m\u001b[43mproxy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     31\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m     32\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhttp2\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m     33\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mTypeError\u001b[39m: Client.__init__() got an unexpected keyword argument 'proxy'"]}], "execution_count": 5}, {"metadata": {}, "cell_type": "code", "source": ["from selenium.webdriver.support import expected_conditions as EC  # Sử dụng đúng import cho EC\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.common.by import By\n", "import time\n", "\n", "from bs4 import BeautifulSoup\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "# Tùy chỉnh Chrome options\n", "options = webdriver.ChromeOptions()\n", "options.add_argument(\"--incognito\")  # Mở ở chế độ ẩn danh\n", "options.add_argument('--no-sandbox')\n", "options.add_argument('--disable-dev-shm-usage')\n", "options.add_argument('--verbose')\n", "\n", "# Khởi tạo dịch vụ Chrome\n", "service = Service()\n", "\n", "# Khởi tạo Chrome với các options đã thiết lập\n", "driver = webdriver.Chrome(service=service, options=options)\n", "\n", "# Mở trang Yahoo Auctions\n", "driver.get(\"https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won\")\n", "\n", "\n", "# Tìm phần tử input để điền thông tin đăng nhập (kiểm tra lại id của trường input)\n", "try:\n", "    login_field = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"login_handle\"))\n", "    )\n", "    login_field.send_keys(\"08063896022\")  # <PERSON><PERSON><PERSON><PERSON> thông tin tài khoản của bạn\n", "except Exception as e:\n", "    print(f\"<PERSON><PERSON><PERSON><PERSON> tìm thấy trường đăng nhập: {e}\")\n", "\n", "# Tì<PERSON> nút \"次へ\" và nhấn vào nó\n", "try:\n", "    next_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[text()='次へ']\"))\n", "    )\n", "    next_button.click()\n", "except Exception as e:\n", "    print(f\"Không tìm thấy nút '次へ': {e}\")\n", "\n", "# Đợi trang tải tiếp (có thể điều chỉnh lại thời gian nếu trang tải chậm)\n", "time.sleep(2)\n", "\n", "# Tì<PERSON> nút \"次へ\" và nhấn vào nó\n", "try:\n", "    next_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[text()='認証を開始']\"))\n", "    )\n", "    next_button.click()\n", "except Exception as e:\n", "    print(f\"Không tìm thấy nút '認証を開始': {e}\")\n", "\n", "time.sleep(10)\n", "\n", "\n", "\n", "# <PERSON><PERSON><PERSON> nội dung của trang và sử dụng BeautifulSoup để phân tích\n", "page_source = driver.page_source\n", "soup = BeautifulSoup(page_source, 'html.parser')\n", "# <PERSON><PERSON><PERSON> trình du<PERSON>t sau khi xong\n", "# driver.quit()\n"], "id": "e4a7311620d868e5", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["\n", "import base64\n", "import os\n", "from selenium.webdriver.common.by import By\n", "\n", "\n", "def download_invoice(output_pdf_path, contact_url):\n", "    # Mở trang web\n", "    driver.get(contact_url)  # Thay bằng URL thật\n", "\n", "    # <PERSON><PERSON><PERSON> cho đến khi trans tải xong (có thể thêm WebDriverWait nếu cần)\n", "    time.sleep(1)\n", "\n", "    # Tìm phần tử chứa link \"支払い明細\" và lấy URL\n", "    try:\n", "        payment_detail_elements = driver.find_elements(By.XPATH, \"//a[contains(text(),'支払い明細')]\")\n", "        payment_detail_element = payment_detail_elements[0] if payment_detail_elements else None\n", "        if payment_detail_element:\n", "            payment_detail_url = payment_detail_element.get_attribute(\"href\")\n", "            driver.get(payment_detail_url)\n", "            # Chờ trang tải đầy đủ (có thể điều chỉnh điều kiện chờ)\n", "            WebDriverWait(driver, 20).until(\n", "                EC.presence_of_element_located((By.TAG_NAME, \"body\"))  # <PERSON><PERSON> phần tử 'body' xu<PERSON>t hiện\n", "            )\n", "\n", "        elif not payment_detail_element and \"buy.auctions\" in contact_url:\n", "            driver.get(contact_url.replace(\"status?\", \"receipt?\"))\n", "            time.sleep(2)\n", "        else:\n", "            print(\"Không tìm thấy link '支払い明細'\")\n", "            return\n", "\n", "    except Exception as e:\n", "        print(f\"Lỗi khi tìm phần tử chứa link '支払い明細': {e}\")\n", "        return\n", "\n", "    # Sử dụng Chrome DevTools Protocol để in trang ra PDF\n", "    result = driver.execute_cdp_cmd(\"Page.printToPDF\", {\"printBackground\": True})\n", "\n", "    # <PERSON><PERSON><PERSON> tra kết quả trả về\n", "    if 'data' not in result or not result['data']:\n", "        print(\"Failed to generate PDF. Result is empty.\")\n", "        return\n", "\n", "    # Trích xuất dữ liệu PDF từ kết quả trả về (Base64) và giải mã\n", "    pdf_data = base64.b64decode(result['data'])\n", "\n", "    # Ghi dữ liệu PDF vào file\n", "    with open(output_pdf_path, 'wb') as file:\n", "        file.write(pdf_data)\n", "\n", "    print(f\"Trang web đã đ<PERSON><PERSON><PERSON> lưu thành PDF tại: {output_pdf_path}\")\n", "\n", "\n", "auctions = supabase.table(\"auctions\").select(\"id,plan_id,contact_url\").eq(\"plan_id\", plan_id).eq(\"status\",\n", "                                                                                           \"finished\").execute().data\n", "print(\"<PERSON><PERSON><PERSON> thấy\", len(auctions), \"đấu giá đã kết thúc\")"], "id": "77e3702e336e7397", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["\n", "for auction in auctions:\n", "    auction_id = auction[\"id\"]\n", "    contact_url = auction[\"contact_url\"]\n", "    os.makedirs(f\"invoices/{plan_id}\", exist_ok=True)\n", "    output_pdf_path = f\"invoices/{plan_id}/{auction_id}.pdf\"\n", "    if os.path.exists(output_pdf_path):\n", "        continue\n", "    else:\n", "        print(f\"Downloading invoice for auction {auction_id}\",contact_url)\n", "        download_invoice(output_pdf_path,contact_url)"], "id": "94f48fb7ef769809", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["import os\n", "from PyPDF2 import PdfReader, PdfWriter\n", "\n", "def keep_first_page(input_pdf_path, output_pdf_path):\n", "    \"\"\"\n", "    G<PERSON><PERSON> lại trang đầu tiên của file PDF nếu có từ 2 trang trở lên.\n", "\n", "    :param input_pdf_path: Đường dẫn tới file PDF gốc\n", "    :param output_pdf_path: Đường dẫn để lưu file PDF kết quả\n", "    \"\"\"\n", "    try:\n", "        # Đọc file PDF đầu vào\n", "        reader = PdfReader(input_pdf_path)\n", "\n", "        # <PERSON><PERSON><PERSON> tra số lượng trang\n", "        if len(reader.pages) >= 2:\n", "            # Ghi lại chỉ trang đầu tiên\n", "            writer = PdfWriter()\n", "            writer.add_page(reader.pages[0])\n", "    \n", "            # Lưu file PDF kết quả\n", "            with open(output_pdf_path, \"wb\") as output_file:\n", "                writer.write(output_file)\n", "        else:\n", "            # copy file\n", "            os.system(f\"cp {input_pdf_path} {output_pdf_path}\")\n", "    except Exception as e:\n", "        print(f\"Đ<PERSON> x<PERSON>y ra lỗi với file {input_pdf_path}: {e}\")\n", "\n", "# <PERSON><PERSON><PERSON> tất cả các file từ thư mục invoices/2\n", "input_directory = f\"invoices/{plan_id}\"\n", "output_directory = \"invoices/processed\"\n", "os.makedirs(output_directory, exist_ok=True)\n", "\n", "for file_name in os.listdir(input_directory):\n", "    if file_name.endswith(\".pdf\"):\n", "        input_path = os.path.join(input_directory, file_name)\n", "        output_path = os.path.join(output_directory, file_name)\n", "        keep_first_page(input_path, output_path)\n"], "id": "bd558402fec933fb", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["import os\n", "from PyPDF2 import PdfReader, PdfWriter\n", "\n", "def merge_pdfs(input_directory, output_pdf_path):\n", "    \"\"\"\n", "    <PERSON><PERSON><PERSON><PERSON> toàn bộ các file PDF trong một thư mục thành một file PDF duy nhất.\n", "\n", "    :param input_directory: <PERSON><PERSON><PERSON> m<PERSON> ch<PERSON>a các file PDF\n", "    :param output_pdf_path: Đường dẫn file PDF đầu ra\n", "    \"\"\"\n", "    try:\n", "        writer = PdfWriter()\n", "\n", "        # <PERSON><PERSON><PERSON><PERSON> qua tất cả các file trong thư mục\n", "        for file_name in sorted(os.listdir(input_directory)):\n", "            if file_name.endswith(\".pdf\"):\n", "                input_pdf_path = os.path.join(input_directory, file_name)\n", "\n", "                # Đọc file PDF\n", "                reader = PdfReader(input_pdf_path)\n", "\n", "                # Th<PERSON><PERSON> tất cả các trang của file vào file kết quả\n", "                for page in reader.pages:\n", "                    writer.add_page(page)\n", "\n", "        # Lưu file PDF kết quả\n", "        with open(output_pdf_path, \"wb\") as output_file:\n", "            writer.write(output_file)\n", "\n", "        print(f\"Đã ghép file PDF và lưu tại: {output_pdf_path}\")\n", "\n", "    except Exception as e:\n", "        print(f\"Đã xảy ra lỗi khi ghép file PDF: {e}\")\n", "\n", "# Đường dẫn thư mục và file đầu ra\n", "input_directory = \"invoices/processed\"\n", "output_pdf_path = f\"invoices/{plan_id}/Invoices_{plan_id}.pdf\"\n", "\n", "# G<PERSON>i hàm ghép file PDF\n", "merge_pdfs(input_directory, output_pdf_path)\n"], "id": "20a373c57eb74152", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["import os\n", "\n", "# Force remove processed files\n", "\n", "for file_name in os.listdir(\"invoices/processed\"):\n", "    file_path = os.path.join(\"invoices/processed\", file_name)\n", "    os.remove(file_path)\n", "\n", "os.removedirs(\"invoices/processed\")\n"], "id": "a2f651ce780ad680", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "", "id": "ade3ff398d31b56", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}