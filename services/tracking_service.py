from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time

class TrackingService:
    def __init__(self, driver, supabase_client):
        self.driver = driver
        self.supabase = supabase_client
    
    def update_yamato_tracking(self):
        """Update tracking information for Yamato packages."""
        # Get auctions with Yamato delivery method
        yamatos = self.supabase.table('auctions').select("*").eq("delivery_method", "ヤマト運輸").neq("status", "finished").execute().data
        
        # Extract tracking numbers
        tracking_numbers = [yamato['tracking_number'] for yamato in yamatos]
        # Filter out None values
        tracking_numbers = [tracking_number for tracking_number in tracking_numbers if tracking_number]
        
        # Process tracking numbers in chunks of 8
        tracking_number_chunks = [tracking_numbers[i:i+8] for i in range(0, len(tracking_numbers), 8)]
        
        for chunk in tracking_number_chunks:
            self.get_yamato_tracking_status(chunk)
    
    def get_yamato_tracking_status(self, tracking_numbers):
        """Get tracking status for Yamato packages."""
        try:
            # Open Yamato tracking page
            self.driver.get("https://toi.kuronekoyamato.co.jp/cgi-bin/tneko")
            
            # Enter tracking numbers
            for i, number in enumerate(tracking_numbers):
                input_box = self.driver.find_element(By.NAME, f"number0{i+1}")
                input_box.clear()
                input_box.send_keys(number)
            
            # Find and click inquiry button
            inquiry_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'お問い合わせ開始')]")
            for button in inquiry_buttons:
                try:
                    # Only click on interactive button
                    WebDriverWait(self.driver, 2).until(EC.element_to_be_clickable(button))
                    button.click()
                    break
                except:
                    continue
            
            # Wait for page to load
            time.sleep(1)
            
            # Find elements containing status information
            status_elements = self.driver.find_elements(By.CLASS_NAME, "area-1")
            for element in status_elements:
                try:
                    input_element = element.find_element(By.XPATH, "div[@class='cell-1']//input[@type='text']")
                    status_element = element.find_element(By.XPATH, "div[@class='cell-2']")
                    
                    tracking_number_value = input_element.get_attribute("value")
                    status_value = status_element.text
                    
                    # Keep only digits in tracking number
                    tracking_number_value = ''.join(filter(str.isdigit, tracking_number_value))
                    
                    # Update status in database
                    self.supabase.table('auctions').update({"delivery_status": status_value}).eq("tracking_number", tracking_number_value).execute()
                    print(f"Updated status for {tracking_number_value}: {status_value}")
                except:
                    print("Could not find input value.")
        
        except Exception as e:
            print(f"Error: {e}")
    
    def update_sagawa_tracking(self):
        """Update tracking information for Sagawa packages."""
        # Get auctions with Sagawa delivery method
        sagawas = self.supabase.table('auctions').select("*").eq("delivery_method", "佐川急便").neq("status", "finished").execute().data
        
        # Extract tracking numbers
        tracking_numbers = [sagawa['tracking_number'] for sagawa in sagawas]
        # Filter out None values
        tracking_numbers = [tracking_number for tracking_number in tracking_numbers if tracking_number]
        
        # Process tracking numbers in chunks of 8
        tracking_number_chunks = [tracking_numbers[i:i+8] for i in range(0, len(tracking_numbers), 8)]
        
        for chunk in tracking_number_chunks:
            self.get_sagawa_tracking_status(chunk)
    
    def get_sagawa_tracking_status(self, tracking_numbers):
        """Get tracking status for Sagawa packages."""
        try:
            # Open Sagawa tracking page
            self.driver.get("https://k2k.sagawa-exp.co.jp/p/sagawa/web/okurijoinput.jsp")
            
            # Enter tracking numbers
            for i, number in enumerate(tracking_numbers):
                input_box = self.driver.find_element(By.NAME, f"main:no{i+1}")
                input_box.clear()
                input_box.send_keys(number)
                if i == len(tracking_numbers) - 1:
                    input_box.send_keys(Keys.RETURN)
            
            # Wait for page to load
            time.sleep(1)
            
            # Find elements containing status information
            status_elements = self.driver.find_elements(By.CLASS_NAME, "state")
            for element in status_elements:
                # Get parent element of state element
                parent_element = element.find_element(By.XPATH, "../..")
                
                if parent_element:
                    tracking_number = parent_element.text.split("\n")[1]
                    state_value = parent_element.text.split("\n")[2]
                    
                    if tracking_number and state_value:
                        # Keep only digits in tracking number
                        tracking_number_value = ''.join(filter(str.isdigit, tracking_number))
                        
                        # Update status in database
                        self.supabase.table('auctions').update({"delivery_status": state_value}).eq("tracking_number", tracking_number_value).execute()
                        print(f"Updated status for {tracking_number_value}: {state_value}")
        
        except Exception as e:
            print(f"Error: {e}")
    
    def update_all_tracking(self):
        """Update tracking information for all packages."""
        self.update_yamato_tracking()
        self.update_sagawa_tracking()
        
    def update_single_tracking(self, tracking_number, delivery_method):
        """Update tracking information for a single package.
        
        Args:
            tracking_number (str): The tracking number to update
            delivery_method (str): The delivery method (e.g. 'ヤマト運輸', '佐川急便')
            
        Returns:
            dict: The updated tracking information including status
        """
        result = {
            'tracking_number': tracking_number,
            'delivery_method': delivery_method,
            'status': 'unknown',
            'details': ''
        }
        
        try:
            # Clean the tracking number to ensure it only contains digits
            tracking_number = ''.join(filter(str.isdigit, tracking_number))
            
            if not tracking_number:
                result['details'] = 'Invalid tracking number'
                return result
                
            if delivery_method == 'ヤマト運輸':
                # Use Yamato tracking
                self.driver.get("https://toi.kuronekoyamato.co.jp/cgi-bin/tneko")
                
                # Enter tracking number
                input_box = self.driver.find_element(By.NAME, "number01")
                input_box.clear()
                input_box.send_keys(tracking_number)
                
                # Find and click inquiry button
                inquiry_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'お問い合わせ開始')]")
                for button in inquiry_buttons:
                    try:
                        # Only click on interactive button
                        WebDriverWait(self.driver, 2).until(EC.element_to_be_clickable(button))
                        button.click()
                        break
                    except:
                        continue
                
                # Wait for page to load
                time.sleep(1)
                
                # Find elements containing status information
                status_elements = self.driver.find_elements(By.CLASS_NAME, "area-1")
                for element in status_elements:
                    try:
                        input_element = element.find_element(By.XPATH, "div[@class='cell-1']//input[@type='text']")
                        status_element = element.find_element(By.XPATH, "div[@class='cell-2']")
                        
                        tracking_number_value = input_element.get_attribute("value")
                        status_value = status_element.text
                        
                        # Update status in database
                        self.supabase.table('auctions').update({"delivery_status": status_value}).eq("tracking_number", tracking_number).execute()
                        
                        result['status'] = status_value
                        result['details'] = 'Status updated successfully'
                        return result
                    except:
                        pass
                        
            elif delivery_method == '佐川急便':
                # Use Sagawa tracking
                self.driver.get("https://k2k.sagawa-exp.co.jp/p/sagawa/web/okurijoinput.jsp")
                
                # Enter tracking number
                input_box = self.driver.find_element(By.NAME, "main:no1")
                input_box.clear()
                input_box.send_keys(tracking_number)
                input_box.send_keys(Keys.RETURN)
                
                # Wait for page to load
                time.sleep(1)
                
                # Find elements containing status information
                status_elements = self.driver.find_elements(By.CLASS_NAME, "state")
                for element in status_elements:
                    # Get parent element of state element
                    parent_element = element.find_element(By.XPATH, "../..")
                    
                    if parent_element:
                        tracking_number_found = parent_element.text.split("\n")[1]
                        state_value = parent_element.text.split("\n")[2]
                        
                        if tracking_number_found and state_value:
                            # Update status in database
                            self.supabase.table('auctions').update({"delivery_status": state_value}).eq("tracking_number", tracking_number).execute()
                            
                            result['status'] = state_value
                            result['details'] = 'Status updated successfully'
                            return result
            
            elif delivery_method == 'ゆうパック':
                # Use direct URL approach for Japan Post tracking
                # Format the tracking number with hyphens as shown on their website (XXXX-XXXX-XXXX format)
                formatted_tracking = tracking_number
                if len(tracking_number) == 12:
                    formatted_tracking = f"{tracking_number[0:4]}-{tracking_number[4:8]}-{tracking_number[8:12]}"
                
                # Navigate directly to search results page
                search_url = f"https://trackings.post.japanpost.jp/services/srv/search/?requestNo1={tracking_number}&search=追跡スタート&locale=ja"
                self.driver.get(search_url)
                
                # Wait for page to load
                time.sleep(3)
                
                try:
                    # Get page source and check if the tracking number exists on the page
                    page_source = self.driver.page_source
                    
                    # Check if the tracking number is displayed on the page
                    if formatted_tracking in page_source or tracking_number in page_source:
                        # Try different patterns to find the status
                        # 1. First try to find the 配送履歴 table (second table)
                        history_tables = self.driver.find_elements(By.CSS_SELECTOR, "table.tableType01")
                        
                        if len(history_tables) >= 2:
                            # Get the history table (second table)
                            history_table = history_tables[1]
                            
                            # Find all rows in the table
                            rows = history_table.find_elements(By.TAG_NAME, "tr")
                            
                            if len(rows) >= 4:  # We need at least 4 rows (2 header rows + 1 data row + its subrow)
                                # Get the first status from the 配送履歴 column
                                try:
                                    # Try to get first data row and access 配送履歴 cell (second column)
                                    status_cell = rows[3].find_elements(By.TAG_NAME, "td")[1]
                                    if status_cell:
                                        status_value = status_cell.text.strip()
                                        if status_value:
                                            # Update status in database
                                            self.supabase.table('auctions').update({"delivery_status": status_value}).eq("tracking_number", tracking_number).execute()
                                            
                                            result['status'] = status_value
                                            result['details'] = 'Status updated successfully'
                                            return result
                                except Exception as inner_e:
                                    print(f"Error extracting status cell: {inner_e}")
                            
                        # If we failed to get status the normal way, look for any status text
                        # Define statuses in order of progression (from earliest to latest)
                        possible_statuses = ["引受", "中継", "到着", "配達完了", "保管期間経過"]
                        
                        # Find the latest status (last one in the delivery process that appears on the page)
                        found_status = None
                        for status in possible_statuses:
                            if status in page_source:
                                found_status = status
                        
                        # If we found a status, update it
                        if found_status:
                            # Update status in database
                            self.supabase.table('auctions').update({"delivery_status": found_status}).eq("tracking_number", tracking_number).execute()
                            
                            result['status'] = found_status
                            result['details'] = 'Latest status found in page'
                            return result
                                
                except Exception as e:
                    print(f"Japan Post tracking error: {e}")
                    
                # If we couldn't extract the status but the page loaded, at least return checked
                if "tableType01" in self.driver.page_source:
                    result['status'] = 'checked'
                    result['details'] = 'Page loaded but unable to extract specific status'
                    return result
            
            elif delivery_method == '西濃運輸':
                # Use Seino tracking
                self.driver.get("https://track.seino.co.jp/kamotsu/GempyoNoShokai.do")
                
                # Enter tracking number
                input_box = self.driver.find_element(By.NAME, "gempyoNo")
                input_box.clear()
                input_box.send_keys(tracking_number)
                
                # Click search button
                search_button = self.driver.find_element(By.CSS_SELECTOR, "input[type='submit']")
                search_button.click()
                
                # Wait for page to load
                time.sleep(2)
                
                try:
                    # Try to find status in the result table
                    table_rows = self.driver.find_elements(By.CSS_SELECTOR, "table.data tr")
                    if len(table_rows) > 1:  # Skip header row
                        # Status is in the second row, third column
                        status_element = table_rows[1].find_elements(By.TAG_NAME, "td")[2]
                        if status_element:
                            status_value = status_element.text.strip()
                            
                            # Update status in database
                            self.supabase.table('auctions').update({"delivery_status": status_value}).eq("tracking_number", tracking_number).execute()
                            
                            result['status'] = status_value
                            result['details'] = 'Status updated successfully'
                            return result
                except:
                    pass
            
            else:
                # Use general tracking approach
                # For 福山通運 and other carriers, we'll use a direct link to their tracking page
                tracking_urls = {
                    '福山通運': f"https://corp.fukutsu.co.jp/situation/tracking_no/{tracking_number}"
                }
                
                if delivery_method in tracking_urls:
                    # Open the tracking page
                    tracking_url = tracking_urls[delivery_method]
                    self.driver.get(tracking_url)
                    
                    # Set a generic success status since we can't easily parse these pages
                    result['status'] = 'checked'
                    result['details'] = f'Opened tracking page for {delivery_method}. Please check the browser for details.'
                    return result
                else:
                    result['details'] = f'Unsupported delivery method: {delivery_method}'
                    return result
            
            # If we get here, no status was found
            result['details'] = 'Tracking information not found'
            return result
            
        except Exception as e:
            result['details'] = f'Error: {str(e)}'
            return result
