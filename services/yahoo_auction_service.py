from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
import time
from datetime import datetime

from utils.selenium_utils import wait_and_click, wait_for_element
from utils.data_utils import (
    clean_price,
    parse_auction_end_time,
    clean_tracking_number,
    identify_delivery_company
)

class YahooAuctionService:
    def __init__(self, driver, supabase_client, gemini_client):
        self.driver = driver
        self.supabase = supabase_client
        self.gemini_client = gemini_client

    def fetch_won_auctions(self):
        """Fetch won auctions from Yahoo Auctions."""
        self.driver.get("https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won")
        time.sleep(2)

        # Find the table containing auction data
        table = self.driver.find_element(By.XPATH, '//*[@id="acWrContents"]/div/table/tbody/tr/td/table/tbody/tr/td/table[1]/tbody/tr[3]/td/table[2]/tbody')

        # Get all rows in the table (skip the first row which contains headers)
        rows = table.find_elements(By.XPATH, 'tr')[1:]

        data = []

        print(f"Total rows with checkboxes: {len(rows)}")

        # Process each row to extract auction information
        for row in rows:
            try:
                # Get item ID (column 2)
                item_id_element = row.find_element(By.XPATH, './/td[2]')
                item_id = item_id_element.text if item_id_element else "No ID found"

                # Get item URL (column 3, contains <a> tag)
                item_url_element = row.find_element(By.XPATH, './/td[3]//a')
                item_url = item_url_element.get_attribute('href') if item_url_element else "No URL found"

                # Get item name (text of <a> tag in column 3)
                item_name = item_url_element.text if item_url_element else "No Name found"

                # Get price (column 4)
                price_element = row.find_element(By.XPATH, './/td[4]')
                price = price_element.text if price_element else "No Price found"

                # Get auction end time (column 5)
                end_time_element = row.find_element(By.XPATH, './/td[5]')
                end_time = end_time_element.text if end_time_element else "No End Time found"

                # Get contact URL (column 7, contains <a> tag)
                contact_url_element = row.find_elements(By.XPATH, './/td[7]//a[1]')
                contact_url = contact_url_element[0].get_attribute('href') if len(contact_url_element) > 0 else "No Contact URL found"

                # Add information to the list
                data.append({
                    "item_id": item_id,
                    "item_url": item_url,
                    "item_name": item_name,
                    "price": price,
                    "auction_end_time": end_time,
                    "contact_url": contact_url
                })
            except Exception as e:
                print(f"Error processing row: {e}")

        return data

    def process_auction_data(self, data):
        """Process auction data and save to database."""
        processed_data = []

        for item in data:
            contact_url = item['contact_url']

            # Clean price
            price = clean_price(item['price'])

            # Create data object
            auction_data = {
                "id": item['item_id'],
                "url": item['item_url'],
                "name": item['item_name'],
                "price": price,
                "contact_url": contact_url,
                "buy_from": "auctions.yahoo.co.jp"
            }

            # Parse auction end time
            auction_end_time = parse_auction_end_time(item['auction_end_time'])
            if auction_end_time:
                auction_data['auction_end_time'] = auction_end_time.isoformat()

            processed_data.append(auction_data)

        # Get recent auctions to avoid duplicates
        last_auctions = self.supabase.from_('auctions').select().order('auction_end_time', desc=True).limit(100).execute().data

        # Filter out auctions that already exist in the database
        new_auctions = [auction for auction in processed_data if auction['id'] not in [existing['id'] for existing in last_auctions]]

        # Save new auctions to database
        if new_auctions:
            result = self.supabase.from_('auctions').upsert(new_auctions).execute()
            print(f"Saved {len(new_auctions)} new auctions to database")
        else:
            print("No new auctions to save")

        return new_auctions

    def get_buy_detail(self, url):
        """Get details from a buy page."""
        self.driver.get(url)
        time.sleep(1)

        try:
            # Find expand button
            expand_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(),'購入内容を確認する')]"))
            )
            if expand_button:
                expand_button.click()  # Click button to expand information
                time.sleep(2)  # Wait for information to expand
        except Exception:
            print("No expand button found or couldn't click it")

        # Parse HTML
        soup = BeautifulSoup(self.driver.page_source, 'html.parser')

        # Helper function to find tags containing direct text
        def extract_info(label, soup):
            # Find <dt> tag with label name and get value of next <dd> tag
            label_tag = soup.find('dt', string=label)
            if label_tag:
                dd_tag = label_tag.find_next_sibling('dd')
                if dd_tag:
                    return dd_tag.text.strip()
            return None

        # Get delivery method
        delivery_method_raw = extract_info('配送業者', soup)
        delivery_method = identify_delivery_company(delivery_method_raw)

        # Get delivery date
        delivery_date = extract_info('配送希望日', soup)

        # Get delivery time
        delivery_time = extract_info('配送希望時間', soup)

        # Get tracking number
        tracking_number = extract_info('伝票番号', soup)

        # Get payment method
        payment_method = extract_info('支払い方法', soup)

        # Get payment total
        payment_total = extract_info('支払い金額 (税込)', soup)

        if payment_total:
            payment_total = payment_total.replace('円', '').replace(',', '')
            try:
                payment_total = int(payment_total)
            except:
                print(f"Could not convert payment total to integer: {payment_total}")
                payment_total = None

        if tracking_number and "配送状況を調べる" in tracking_number:
            tracking_number = tracking_number.split("配送状況を調べる")[0]

        if not tracking_number:
            # Find all ptsPartner elements
            pts_partner_elements = self.driver.find_elements(By.CSS_SELECTOR, "dl.ptsPartner")
            for element in pts_partner_elements:
                element_text = element.text
                # Check if text contains the Japanese character for tracking (追跡 or 問い合わせ or 問合)
                if "追跡" in element_text or "問い合わせ" in element_text or "問合" in element_text:
                    tracking_number = self.force_crawl_tracking_number(element_text)
                    if tracking_number:
                        break
        return {
            "delivery_method": delivery_method,
            "delivery_date": delivery_date,
            "delivery_time": delivery_time,
            "tracking_number": tracking_number,
            "payment_method": payment_method,
            "payment_total": payment_total,
        }

    def get_detail(self, url):
        """Get details from a transaction page."""
        self.driver.get(url)
        time.sleep(1)

        try:
            # Find expand button
            expand_button = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(),'まとめて取引を確認する')]"))
            )
            if expand_button:
                url = expand_button.get_attribute('href')
                if url:
                    self.driver.get(url)
                    time.sleep(2)
        except Exception:
            print("No expand button found or couldn't click it")

        soup = BeautifulSoup(self.driver.page_source, 'html.parser')

        # Find all elements with class 'acMdStatusImage__chargeText'
        charge_elements = soup.find_all('li', class_='acMdStatusImage__chargeText')

        # Get the last status
        status = ""
        if charge_elements:
            # Filter to get only elements with non-empty content
            non_empty_elements = [element.text.strip() for element in charge_elements if element.text.strip()]

            # Get the last element if any
            status = non_empty_elements[-1] if non_empty_elements else ""

        # Helper functions to find tags based on text
        def find_by_text(soup, text_value):
            return soup.find('div', string=text_value)

        def extract_info(label, soup):
            label_tag = find_by_text(soup, label)
            if label_tag:
                th_tag = label_tag.find_parent('th')
                if th_tag:
                    td_tag = th_tag.find_next_sibling('td')
                    if td_tag:
                        content_tag = td_tag.find('div', class_='decCnfWr') or td_tag.find('a')
                        if content_tag:
                            return content_tag.text.strip()
            return None

        # Get payment information
        payment = extract_info('支払い金額', soup)

        payment_total = None
        price = None
        shipping_fee = None

        if payment:
            # Extract payment total
            payment_total = payment.split('（')[0].replace('円', '').replace(',', '')
            try:
                payment_total = int(payment_total)
            except:
                print(f"Could not convert payment total to integer: {payment_total}")

            # Extract price
            try:
                price = payment.split('（')[1].split('：')[1].split('円')[0].replace(',', '')
                price = int(price) if price else None
            except:
                price = None

            # Extract shipping fee
            try:
                shipping_fee = payment.split('（')[1].split('送料：')[1].split('円')[0].replace(',', '')
                shipping_fee = int(shipping_fee) if shipping_fee else None
            except:
                shipping_fee = None

        # Get delivery method
        delivery_method_raw = extract_info('配送方法', soup)
        delivery_method = identify_delivery_company(delivery_method_raw)

        # Get delivery status
        delivery_status = extract_info('配送状況', soup)

        # Get delivery time
        delivery_time = extract_info('配送希望時間帯', soup)

        # Get payment method
        payment_method = extract_info('支払い方法', soup)

        # Get tracking number
        tracking_number = extract_info('追跡番号', soup)

        if not tracking_number:
            # Find all ptsPartner elements
            pts_partner_elements = self.driver.find_elements(By.CSS_SELECTOR, "dl.ptsPartner")
            for element in pts_partner_elements:
                element_text = element.text
                # Check if text contains the Japanese character for tracking (追跡 or 問い合わせ or 問合)
                if "追跡" in element_text or "問い合わせ" in element_text or "問合" in element_text:
                    tracking_number = self.force_crawl_tracking_number(element_text)
                    if tracking_number:
                        break

        # Check if auction is finished
        auction_status = soup.find('div', class_='ptsBoxOk mB20')

        if auction_status and 'すべての取引が完了しました' in auction_status.text:
            auction_status = 'finished'
        elif delivery_status == "配達完了":
            auction_status = 'finished'
        else:
            auction_status = None

        # Check shipping fee information in delivery method
        if delivery_method_raw:
            if "送料：出品者負担" in delivery_method_raw:
                shipping_fee = "出品者負担"
            if "着払い" in delivery_method_raw:
                shipping_fee = "着払い"

        return {
            "payment_total": payment_total,
            "shipping_fee": shipping_fee,
            "status": auction_status,
            "delivery_method": delivery_method,
            "delivery_time": delivery_time,
            "payment_method": payment_method,
            "delivery_status": delivery_status,
            "tracking_number": tracking_number,
        }

    def force_crawl_tracking_number(self, body_content):
        """Use Gemini API to find tracking number in content."""
        # Use Gemini API to find tracking number
        prompt = """
        Hãy tìm chuỗi tracking number trong phần nội dung dưới đây theo các quy tắc sau:
        Tracking number có độ dài từ 10 đến 15 ký tự, chỉ gồm chữ số half‐width (0–9), không bao gồm dấu gạch ngang ('-') hay ký tự full‐width.
        Nếu nội dung có chuỗi dạng có gạch ngang (ví dụ "４９８０－５９５１ー３７８１" hoặc "4980-5951-3781"), hãy loại bỏ mọi dấu gạch ngang và chuyển các chữ số full‐width sang half‐width trước khi kiểm tra.
        Sau khi xử lý, nếu tìm được một chuỗi chỉ toàn số half‐width có độ dài từ 10 đến 15, hãy trả về chính xác chuỗi đó.
            Nếu không tìm thấy, chỉ trả về chuỗi rỗng: "" (không in thêm bất kỳ ký tự nào khác). Tôi chỉ mã tracking và không cần gì khác

            Dưới đây là nội dung:
            """ + body_content

        try:
            model = self.gemini_client.GenerativeModel('gemini-2.5-flash-preview-04-17')
            response = model.generate_content(prompt)
            content = response.text
            if content and content.strip() and content.isdigit():
                print(f"Using Gemini API, found tracking number: {content}")
                return content
        except Exception as e:
            print(f"Error using Gemini API: {e}")
        return None

    def process_pending_auctions(self):
        """Process auctions that need tracking information."""
        # Get auctions that need processing
        auctions = self.supabase.from_('auctions').select().eq('buy_from', 'auctions.yahoo.co.jp').neq('status', 'finished').is_(
            'tracking_number', None).order('auction_end_time', desc=True).execute().data

        print(f"Number of auctions to process: {len(auctions)}")

        for auction in auctions:
            print(f"{auction['id']} {auction['name']} {auction['price']} {auction['auction_end_time']} {auction['contact_url']}")

            if auction['contact_url']:
                detail = None

                if "buy.auc" in auction['contact_url']:
                    detail = self.get_buy_detail(auction['contact_url'])
                else:
                    detail = self.get_detail(auction['contact_url'])

                if detail:
                    # Process delivery date
                    if "delivery_date" in detail and detail["delivery_date"]:
                        if "年" in detail["delivery_date"]:
                            # Convert to datetime
                            try:
                                detail["delivery_date"] = datetime.strptime(detail["delivery_date"], '%Y年%m月%d日').isoformat()
                            except:
                                detail["delivery_date"] = None
                        else:
                            detail["delivery_date"] = None

                    # Clean tracking number
                    if "tracking_number" in detail and detail["tracking_number"]:
                        detail["tracking_number"] = clean_tracking_number(detail["tracking_number"])

                    # Set status based on delivery_status or default
                    if "delivery_status" in detail and detail["delivery_status"] == "配達完了":
                        detail["status"] = 'finished'
                    elif "status" not in detail or not detail["status"]:
                        detail["status"] = 'processing'

                    # Set buy_from
                    detail['buy_from'] = 'auctions.yahoo.co.jp'

                    # Ensure payment_total is an integer
                    if 'payment_total' in detail and detail['payment_total']:
                        try:
                            detail['payment_total'] = int(detail['payment_total'])
                        except:
                            detail['payment_total'] = None

                    # Update auction in database
                    self.supabase.from_('auctions').update(detail).eq('id', auction['id']).execute()
                    print(f"Updated auction {auction['id']}")

    def process_feedback(self):
        """Process auctions that need feedback."""
        print("\n=== STARTING FEEDBACK PROCESSING ===")
        # Get auctions that need feedback using RPC
        try:
            # Call the get_need_feedback RPC
            print("Fetching auctions needing review using get_need_feedback RPC")
            response = self.supabase.rpc('get_need_feedback').execute()
            need_feedback = response.data
            
            # Print the query results
            print(f"Number of auctions needing review: {len(need_feedback)}")
            if need_feedback:
                for i, item in enumerate(need_feedback):
                    print(f"  {i+1}. ID: {item['id']}, Name: {item['name']}")
                    print(f"     Delivery status: {item.get('delivery_status', 'Not set')}")
                    print(f"     Reviewed at: {item.get('reviewed_at', 'Not set')}")
            
        except Exception as e:
            print(f"Error fetching feedback needed auctions: {e}")
            need_feedback = []
            print("Number of auctions needing review: 0")
        
        # Rest of the method remains the same
        for idx, auction in enumerate(need_feedback):
            print(f"\n--- Processing auction {idx+1}/{len(need_feedback)} ---")
            print(f"Auction: {auction['id']} - {auction['name']}")
            print(f"Contact URL: {auction['contact_url']}")
            
            self.driver.get(auction['contact_url'])
            print(f"Loaded contact URL page")
            
            url = self.get_feedback_url(auction['contact_url'])
            print(f"Feedback URL: {url if url else 'Not found'}")

            if url:
                # Navigate to feedback page
                print(f"Navigating to feedback URL")
                self.driver.get(url)
                time.sleep(2)

                try:
                    # Find and click common text button
                    print("Attempting to find common text button")
                    common_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.ID, "commonTextIn"))
                    )
                    if common_button:
                        print("Found common text button, clicking")
                        common_button.click()
                        time.sleep(1)

                        # Find and click confirm button
                        print("Attempting to find confirm button")
                        confirm_button = WebDriverWait(self.driver, 3).until(
                            EC.element_to_be_clickable((By.XPATH, "//input[@value='確認する']"))
                        )
                        if confirm_button:
                            print("Found confirm button, clicking")
                            confirm_button.click()
                            time.sleep(1)

                            # Find and click publish button
                            print("Attempting to find publish button")
                            send_button = WebDriverWait(self.driver, 3).until(
                                EC.element_to_be_clickable((By.XPATH, "//input[@value='評価を公開する']"))
                            )
                            if send_button:
                                print("Found publish button, clicking")
                                send_button.click()
                                time.sleep(1)

                                # Update review status in database
                                print("Review submitted successfully, updating database")
                                self.supabase.table('auctions').update({'reviewed_at': datetime.now().isoformat()}).eq('id',
                                                                                                   auction['id']).execute()
                                print("Database updated with review timestamp")
                            else:
                                print("ERROR: Could not find publish button")
                        else:
                            print("ERROR: Could not find confirm button")
                    else:
                        print("ERROR: Could not find common text button")
                except Exception as e:
                    print(f"ERROR in feedback submission process: {e}")
                    # Print more details about the page content for debugging
                    print("Current page title:", self.driver.title)
                    print("Current URL:", self.driver.current_url)
                    print("Page contains these key elements:")
                    key_texts = ["評価", "確認", "公開", "フィードバック", "コメント"]
                    for text in key_texts:
                        if text in self.driver.page_source:
                            print(f"  - '{text}' found in page")
                        else:
                            print(f"  - '{text}' NOT found in page")
            else:
                # Check if already reviewed
                print("No feedback URL found, checking if already reviewed")
                if "前回、評価した内容" in self.driver.page_source:
                    print("Already reviewed (found '前回、評価した内容'), updating database")
                    self.supabase.table('auctions').update({'reviewed_at': datetime.now().isoformat()}).eq('id',
                                                                                       auction['id']).execute()
                    print("Database updated with review timestamp")
                else:
                    print("Unable to find feedback link and no evidence of previous review")
                    page_indicators = ["取引", "受け取り", "完了", "評価"]
                    for indicator in page_indicators:
                        if indicator in self.driver.page_source:
                            print(f"  - Found '{indicator}' in page")
                        else:
                            print(f"  - '{indicator}' NOT found in page")
        
        print("\n=== FEEDBACK PROCESSING COMPLETED ===")

    def get_feedback_url(self, contact_url):
        """Get URL for feedback page."""
        print(f"Getting feedback URL for contact URL: {contact_url}")
        self.driver.get(contact_url)

        try:
            # Find expand button
            print("Looking for 'まとめて取引を確認する' button")
            expand_button = WebDriverWait(self.driver, 3).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(),'まとめて取引を確認する')]"))
            )
            if expand_button:
                url = expand_button.get_attribute('href')
                print(f"Found expand button, navigating to: {url}")
                self.driver.get(url)
                time.sleep(2)
        except Exception as e:
            print(f"No expand button found: {e}")

        # Try different methods to find feedback link
        print("Trying to find feedback link using multiple methods")
        url = self.get_feedback_url_by_xpath('//a[contains(text(),"出品者を評価する")]')
        if url:
            print(f"Found feedback URL using method 1: {url}")
            return url
            
        url = self.get_feedback_url_by_xpath(
            '//a[@class="libBtnBlueL" and contains(@href, "auctions.yahoo.co.jp") and contains(@href, "出品者を評価する")]')
        if url:
            print(f"Found feedback URL using method 2: {url}")
            return url
            
        url = self.get_feedback_url_by_css('a.libBtnBlueL')
        if url:
            print(f"Found feedback URL using method 3: {url}")
            return url

        # If no feedback link found, check if need to confirm receipt
        print("No feedback link found, checking if receipt confirmation is needed")
        if not url:
            try:
                # Wait for checkbox to appear
                print("Looking for receipt confirmation checkbox")
                checkbox = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.ID, "jsCheckReceive"))
                )

                # Wait for "受け取り連絡" button to appear
                print("Looking for receipt confirmation button")
                button = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'input.libBtnRedL.jsOnReceiveButton'))
                )

                # Check if checkbox and button are displayed
                if checkbox.is_displayed():
                    # Click checkbox
                    print("Found checkbox, clicking it")
                    checkbox.click()
                    # Click "受け取り連絡" button
                    time.sleep(1)
                    print("Clicking receipt confirmation button")
                    button.click()
                    time.sleep(3)
                    print("Receipt confirmed, retrying to get feedback URL")
                    return self.get_feedback_url(contact_url)
                else:
                    print("Checkbox found but not displayed")
            except Exception as e:
                print(f"Could not find receipt confirmation elements: {e}")
                # Dump some page information to help debug
                print("Current page content keywords:")
                important_texts = ["取引", "受け取り", "評価", "確認", "連絡"]
                for text in important_texts:
                    if text in self.driver.page_source:
                        print(f"  - '{text}' found in page")
                    else:
                        print(f"  - '{text}' NOT found in page")

        print(f"Final result: {'Found' if url else 'Did not find'} feedback URL")
        return url

    def get_feedback_url_by_xpath(self, xpath):
        """Get feedback URL using XPath."""
        try:
            print(f"Searching for feedback link using XPath: {xpath}")
            feedback_link = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            result = feedback_link.get_attribute("href") if feedback_link else None
            print(f"Result: {'Found' if result else 'Not found'}")
            return result
        except Exception as e:
            print(f"XPath search failed: {e}")
            return None

    def get_feedback_url_by_css(self, css):
        """Get feedback URL using CSS selector."""
        try:
            print(f"Searching for feedback link using CSS: {css}")
            feedback_link = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, css))
            )
            result = feedback_link.get_attribute("href") if feedback_link else None
            print(f"Result: {'Found' if result else 'Not found'}")
            return result
        except Exception as e:
            print(f"CSS search failed: {e}")
            return None
