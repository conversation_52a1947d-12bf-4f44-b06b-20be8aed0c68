{"cells": [{"cell_type": "code", "execution_count": 50, "id": "65aa49c0929<PERSON>ee", "metadata": {"ExecuteTime": {"end_time": "2025-04-21T00:50:35.230169Z", "start_time": "2025-04-21T00:50:35.215622Z"}, "collapsed": false}, "outputs": [], "source": ["from supabase import create_client, Client\n", "\n", "supabase: Client = create_client(\"https://cduauvwekzwtsmwgsngf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdWF1dndla3p3dHNtd2dzbmdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjkzMDk0MjYsImV4cCI6MjA0NDg4NTQyNn0.V6pQ0FRD69bYQ-CgoiKPH9SXNqdXSs-9QYM_UtqjNVw\")\n"]}, {"cell_type": "code", "execution_count": 51, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-04-21T00:50:57.525261Z", "start_time": "2025-04-21T00:50:39.396845Z"}, "collapsed": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The chromedriver version (135.0.7049.97) detected in PATH at /usr/local/bin/chromedriver might not be compatible with the detected chrome version (136.0.7103.93); currently, chromedriver 136.0.7103.92 is recommended for chrome 136.*, so it is advised to delete the driver in PATH and retry\n"]}], "source": ["from selenium.webdriver.support import expected_conditions as EC  # Sử dụng đúng import cho EC\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.common.by import By\n", "import time\n", "\n", "from bs4 import BeautifulSoup\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.service import Service\n", "\n", "# Tùy chỉnh Chrome options\n", "options = webdriver.ChromeOptions()\n", "options.add_argument(\"--incognito\")  # Mở ở chế độ ẩn danh\n", "options.add_argument('--no-sandbox')\n", "options.add_argument('--disable-dev-shm-usage')\n", "options.add_argument('--verbose')\n", "\n", "# Khởi tạo dịch vụ Chrome\n", "service = Service()\n", "\n", "# Khởi tạo Chrome với các options đã thiết lập\n", "driver = webdriver.Chrome(service=service, options=options)\n", "\n", "# Mở trang Yahoo Auctions\n", "driver.get(\"https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won\")\n", "\n", "\n", "# Tìm phần tử input để điền thông tin đăng nhập (kiểm tra lại id của trường input)\n", "try:\n", "    login_field = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"login_handle\"))\n", "    )\n", "    login_field.send_keys(\"08063896022\")  # <PERSON><PERSON><PERSON><PERSON> thông tin tài khoản của bạn\n", "except Exception as e:\n", "    print(f\"<PERSON><PERSON><PERSON><PERSON> tìm thấy trường đăng nhập: {e}\")\n", "\n", "# Tì<PERSON> nút \"次へ\" và nhấn vào nó\n", "try:\n", "    next_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[text()='次へ']\"))\n", "    )\n", "    next_button.click()\n", "except Exception as e:\n", "    print(f\"Không tìm thấy nút '次へ': {e}\")\n", "\n", "# Đợi trang tải tiếp (có thể điều chỉnh lại thời gian nếu trang tải chậm)\n", "time.sleep(2)\n", "\n", "# Tì<PERSON> nút \"次へ\" và nhấn vào nó\n", "try:\n", "    next_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[text()='認証を開始']\"))\n", "    )\n", "    next_button.click()\n", "except Exception as e:\n", "    print(f\"Không tìm thấy nút '認証を開始': {e}\")\n", "\n", "time.sleep(10)\n", "\n", "\n", "\n", "# <PERSON><PERSON><PERSON> nội dung của trang và sử dụng BeautifulSoup để phân tích\n", "page_source = driver.page_source\n", "soup = BeautifulSoup(page_source, 'html.parser')\n", "# <PERSON><PERSON><PERSON> trình du<PERSON>t sau khi xong\n", "# driver.quit()\n"]}, {"cell_type": "code", "execution_count": 52, "id": "6122071b65348aef", "metadata": {"ExecuteTime": {"end_time": "2025-04-21T00:51:05.466064Z", "start_time": "2025-04-21T00:50:59.947180Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tổng số row chứa checkbox: 50\n"]}], "source": ["from selenium.webdriver.common.by import By\n", "\n", "driver.get(\"https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won\")\n", "\n", "time.sleep(2)\n", "# get table //*[@id=\"acWrContents\"]/div/table/tbody/tr/td/table/tbody/tr/td/table[1]/tbody/tr[3]/td/table[2]/tbody\n", "\n", "table = driver.find_element(By.XPATH, '//*[@id=\"acWrContents\"]/div/table/tbody/tr/td/table/tbody/tr/td/table[1]/tbody/tr[3]/td/table[2]/tbody')\n", "\n", "# <PERSON><PERSON><PERSON> tất cả các hàng trong bảng (tr, trừ hàng đầu tiên chứa tiêu đề)\n", "rows = table.find_elements(By.XPATH, 'tr')[1:]\n", "\n", "data = []\n", "\n", "# In ra số lượng hàng chứa checkbox\n", "print(f\"Tổng số row chứa checkbox: {len(rows)}\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> qua từng hàng và lấy thông tin\n", "for row in rows:  # Chỉ lấy 5 hàng đầu tiên để kiểm tra\n", "    try:\n", "        # <PERSON><PERSON>y ID (c<PERSON><PERSON> thứ 2)\n", "        item_id_element = row.find_element(By.XPATH, './/td[2]')\n", "        item_id = item_id_element.text if item_id_element else \"No ID found\"\n", "\n", "        # Lấy URL của item (cột thứ 3, chứa thẻ <a>)\n", "        item_url_element = row.find_element(By.XPATH, './/td[3]//a')\n", "        item_url = item_url_element.get_attribute('href') if item_url_element else \"No URL found\"\n", "\n", "        # <PERSON><PERSON><PERSON> tên sản phẩm (văn bản của thẻ <a> trong cột thứ 3)\n", "        item_name = item_url_element.text if item_url_element else \"No Name found\"\n", "\n", "        # <PERSON><PERSON><PERSON> (c<PERSON><PERSON> th<PERSON> 4)\n", "        price_element = row.find_element(By.XPATH, './/td[4]')\n", "        price = price_element.text if price_element else \"No Price found\"\n", "\n", "        # <PERSON><PERSON><PERSON> thời gian đấu gi<PERSON> kết thúc (cột thứ 5)\n", "        end_time_element = row.find_element(By.XPATH, './/td[5]')\n", "        end_time = end_time_element.text if end_time_element else \"No End Time found\"\n", "\n", "        # <PERSON><PERSON>y URL của phần contact (c<PERSON><PERSON> thứ 7, ch<PERSON><PERSON> thẻ <a>)\n", "        contact_url_element = row.find_elements(By.XPATH, './/td[7]//a[1]')\n", "        contact_url = contact_url_element[0].get_attribute('href') if len(contact_url_element) > 0 else \"No Contact URL found\"\n", "\n", "        # Thêm thông tin vào danh sách\n", "        data.append({\n", "            \"item_id\": item_id,\n", "            \"item_url\": item_url,\n", "            \"item_name\": item_name,\n", "            \"price\": price,\n", "            \"auction_end_time\": end_time,\n", "            \"contact_url\": contact_url\n", "        })\n", "    except Exception as e:\n", "        print(f\"Error processing row: {row.text}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 53, "id": "d446d51cddd3dbb3", "metadata": {"ExecuteTime": {"end_time": "2025-04-21T00:51:12.161024Z", "start_time": "2025-04-21T00:51:10.760941Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 50 new auctions to database\n"]}], "source": ["from datetime import datetime\n", "\n", "\n", "# Hàm chuyển datetime thành chuỗi ISO 8601\n", "def custom_encoder(obj):\n", "    if isinstance(obj, datetime):\n", "        return obj.isoformat()  # Định dạng ISO 8601 (YYYY-MM-DDTHH:MM:SS)\n", "    raise TypeError(f\"Object of type {obj.__class__.__name__} is not JSON serializable\")\n", "\n", "_vars = []\n", "\n", "for item in data:\n", "    contact_url = item['contact_url']\n", "\n", "    if \"円\" in item['price']:\n", "        price = int(item['price'].replace(\",\", \"\").replace(\"円\", \"\").replace(\" \", \"\"))\n", "    else:\n", "        price = 0\n", "\n", "    var = {\n", "        \"id\": item['item_id'],\n", "        \"url\": item['item_url'],\n", "        \"name\": item['item_name'],\n", "        \"price\": price,\n", "        \"contact_url\": contact_url,\n", "        \"buy_from\": \"auctions.yahoo.co.jp\"  # Add buy_from field to match schema\n", "    }\n", "\n", "    if \"\\n\" in item['auction_end_time']:\n", "        item['auction_end_time'] = item['auction_end_time'].split(\"\\n\")[0]\n", "    try:\n", "        # Add explicit year to the format string to avoid deprecation warning\n", "        current_year = datetime.now().year\n", "        auction_end_time = datetime.strptime(f\"{current_year}年{item['auction_end_time']}\", f'%Y年%m月%d日 %H時%M分')\n", "        var['auction_end_time'] = auction_end_time.isoformat()\n", "    except ValueError:\n", "        print(f\"Error parsing date: {item['auction_end_time']}\")\n", "        var['auction_end_time'] = None\n", "    \n", "    _vars.append(var)\n", "\n", "try:\n", "    # Get recent auctions to avoid duplicates\n", "    last_auctions = supabase.from_('auctions').select().order('auction_end_time', desc=True).limit(100).execute().data\n", "    \n", "    # Filter out auctions that already exist in the database\n", "    new_auctions = [var for var in _vars if var['id'] not in [auction['id'] for auction in last_auctions]]\n", "    \n", "    # Save new auctions to database\n", "    if new_auctions:\n", "        result = supabase.from_('auctions').upsert(new_auctions).execute()\n", "        print(f\"Saved {len(new_auctions)} new auctions to database\")\n", "    else:\n", "        print(\"No new auctions to save\")\n", "except Exception as e:\n", "    print(f\"Error when saving to database: {str(e)}\")\n"]}, {"cell_type": "code", "execution_count": 54, "id": "8532dc11b911f10e", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:27.658114Z", "start_time": "2025-02-17T23:31:27.651496Z"}, "collapsed": false}, "outputs": [], "source": ["\n", "def get_buy_detail(url):\n", "    \n", "    driver.get(url)\n", "    time.sleep(1)\n", "    try:    \n", "        # Tìm nút mở rộng thông tin\n", "        \n", "        expand_button = WebDriverWait(driver, 10).until(\n", "            EC.element_to_be_clickable((By.XPATH, \"//a[contains(text(),'購入内容を確認する')]\"))\n", "        )\n", "        if expand_button:\n", "            expand_button.click()  # Nhấn vào nút để mở rộng thông tin\n", "            time.sleep(2)  # <PERSON><PERSON><PERSON> một chút để chắc chắn thông tin đã mở rộng\n", "    except Exception:\n", "        print(f\"\")\n", "    \n", "    # Parse HTML\n", "    soup = BeautifulSoup(driver.page_source, 'html.parser')\n", "\n", "    # Hàm helper để tìm thẻ chứa văn bản trực tiếp\n", "    def extract_info(label, soup):\n", "        # Tìm thẻ <dt> với tên label và lấy giá trị của <dd> kế tiếp\n", "        label_tag = soup.find('dt', string=label)\n", "        if label_tag:\n", "            dd_tag = label_tag.find_next_sibling('dd')\n", "            if dd_tag:\n", "                return dd_tag.text.strip()\n", "        return None\n", "\n", "    # <PERSON><PERSON>y thông tin từ form mới\n", "\n", "\n", "  \n", "    # <PERSON><PERSON><PERSON> thông tin 配送業者 (Đơn vị vận chuyển)\n", "    delivery_method_raw = extract_info('配送業者', soup)\n", "    delivery_method = delivery_method_raw\n", "    if delivery_method_raw:\n", "        if \"ヤマト\" in delivery_method_raw or \"おてがる配送\" in delivery_method_raw:\n", "            delivery_method = \"ヤマト運輸\"\n", "        elif \"佐川\" in delivery_method_raw:\n", "            delivery_method = \"佐川急便\"\n", "        elif \"カンガルー\" in delivery_method_raw or \"西濃\" in delivery_method_raw:\n", "            delivery_method = \"西濃運輸\"\n", "        elif \"ゆうパック\" in delivery_method_raw:\n", "            delivery_method = \"ゆうパック\"\n", "        elif \"福山\" in delivery_method_raw:\n", "            delivery_method = \"福山通運\"\n", "    \n", "# <PERSON><PERSON><PERSON> thông tin 配送希望日 (<PERSON><PERSON><PERSON> giao hàng mong muốn)\n", "    delivery_date = extract_info('配送希望日', soup)\n", "    \n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 配送希望時間 (<PERSON>h<PERSON><PERSON> gian giao hàng mong muốn)\n", "    delivery_time = extract_info('配送希望時間', soup)\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 伝票番号 (Số vận đơn)\n", "    tracking_number = extract_info('伝票番号', soup)\n", "    \n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 支払い方法 (<PERSON><PERSON><PERSON><PERSON> thức thanh toán)\n", "    payment_method = extract_info('支払い方法', soup)\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 支払い金額 (<PERSON><PERSON> tiền thanh toán)\n", "    payment_total = extract_info('支払い金額 (税込)', soup)\n", "    \n", "    if payment_total:\n", "        payment_total = payment_total.replace('円', '').replace(',', '')\n", "        # convert to number\n", "        if payment_total:\n", "            try:\n", "                payment_total = int(payment_total)\n", "            except:\n", "                print(payment_total)\n", "\n", "    if tracking_number and \"配送状況を調べる\" in tracking_number:\n", "        tracking_number = tracking_number.split(\"配送状況を調べる\")[0]\n", "\n", "    # In kết quả\n", "    return {\n", "        \"delivery_method\": delivery_method,\n", "        \"delivery_date\": delivery_date,\n", "        \"delivery_time\": delivery_time,\n", "        \"tracking_number\": tracking_number,\n", "        \"payment_method\": payment_method,\n", "        \"payment_total\": payment_total,\n", "    }\n"]}, {"cell_type": "code", "execution_count": 55, "id": "7d26bea347a95755", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:27.723494Z", "start_time": "2025-02-17T23:31:27.712820Z"}, "collapsed": false}, "outputs": [], "source": ["# Đ<PERSON><PERSON> cho đến khi trang tải hoàn toàn và tìm liên kết \"支払い明細\"\n", "def get_detail(url):\n", "    driver.get(url)\n", "    time.sleep(1)\n", "\n", "    try:\n", "    # Tìm nút mở rộng thông tin\n", "\n", "        expand_button = WebDriverWait(driver, 10).until(\n", "            EC.element_to_be_clickable((By.XPATH, \"//a[contains(text(),'まとめて取引を確認する')]\"))\n", "        )\n", "        if expand_button:\n", "            url = expand_button.get_attribute('href')\n", "            if url:\n", "                driver.get(url)\n", "                time.sleep(2)\n", "    except Exception:\n", "        print(f\"\")\n", "\n", "    soup = BeautifulSoup(driver.page_source, 'html.parser')\n", "    # Tì<PERSON> tất cả các phần tử có class 'acMdStatusImage__chargeText'\n", "    charge_elements = soup.find_all('li', class_='acMdStatusImage__chargeText')\n", "    \n", "    # <PERSON><PERSON><PERSON> trạng thái cuối cùng\n", "    if charge_elements:\n", "        # L<PERSON><PERSON> để chỉ lấy phần tử có nội dung không rỗng\n", "        non_empty_elements = [element.text.strip() for element in charge_elements if element.text.strip()]\n", "    \n", "        # <PERSON><PERSON><PERSON> phần tử cuối cùng nếu có\n", "        status = non_empty_elements[-1] if non_empty_elements else \"\"\n", "    else:\n", "        status = \"\"\n", "    # Hàm helper để tìm thẻ dựa trên văn bản\n", "    # Hàm helper để tìm thẻ chứa văn bản trực tiếp\n", "    def find_by_text(soup, text_value):\n", "        return soup.find('div', string=text_value)\n", "\n", "    def extract_info(label, soup):\n", "        label_tag = find_by_text(soup, label)\n", "        if label_tag:\n", "            th_tag = label_tag.find_parent('th')\n", "            if th_tag:\n", "                td_tag = th_tag.find_next_sibling('td')\n", "                if td_tag:\n", "                    content_tag = td_tag.find('div', class_='decCnfWr') or td_tag.find('a')\n", "                    if content_tag:\n", "                        return content_tag.text.strip()\n", "        return None\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 支払い金額 (<PERSON><PERSON> tiền thanh toán)\n", "    payment = extract_info('支払い金額', soup)\n", "    \n", "    # payment_total = '10,400円（落札金額：8,800円\\xa0数量：1個\\xa0送料：1,600円）'\n", "    if payment:\n", "        payment_total = payment.split('（')[0].replace('円', '').replace(',', '')\n", "        # convert to number\n", "        if payment_total:\n", "            try:\n", "                payment_total = int(payment_total)\n", "            except:\n", "                print(payment_total)\n", "        try:\n", "            price = payment.split('（')[1].split('：')[1].split('円')[0].replace(',', '')\n", "            if price:\n", "                price = int(price)\n", "        except:\n", "            price = None\n", "        # get送料 by regex\n", "        try:\n", "            shipping_fee = payment.split('（')[1].split('送料：')[1].split('円')[0].replace(',', '')\n", "            if shipping_fee:\n", "                try:\n", "                    shipping_fee = int(shipping_fee)\n", "                except:\n", "                    pass\n", "        except:\n", "            shipping_fee = None\n", "    else:\n", "        payment_total = None\n", "        shipping_fee = None\n", "\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 住所 (Đ<PERSON><PERSON> chỉ)\n", "    #address = extract_info('住所', soup)\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 配送方法 (<PERSON><PERSON><PERSON><PERSON> thức giao hàng)\n", "    delivery_method_raw = extract_info('配送方法', soup)\n", "    delivery_method = delivery_method_raw\n", "    \n", "    if delivery_method_raw: \n", "        if \"ヤマト\" in delivery_method_raw or \"おてがる配送\" in delivery_method_raw:\n", "            delivery_method = \"ヤマト運輸\"\n", "        elif \"佐川\" in delivery_method_raw:\n", "            delivery_method = \"佐川急便\"\n", "        elif \"カンガルー\" in delivery_method_raw or \"西濃\" in delivery_method_raw:\n", "            delivery_method = \"西濃運輸\"\n", "        elif \"ゆうパック\" in delivery_method_raw:\n", "            delivery_method = \"ゆうパック\"\n", "        elif \"福山\" in delivery_method_raw:\n", "            delivery_method = \"福山通運\"\n", "    \n", "    # 配送状況\n", "    \n", "    delivery_status = extract_info('配送状況', soup)\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 配送希望時間帯 (<PERSON><PERSON><PERSON><PERSON> gian giao hàng mong muốn)\n", "    delivery_time = extract_info('配送希望時間帯', soup)\n", "\n", "    # <PERSON><PERSON><PERSON> thông tin 支払い方法 (<PERSON><PERSON><PERSON><PERSON> thức thanh toán)\n", "    payment_method = extract_info('支払い方法', soup)\n", "    tracking_number = extract_info('追跡番号', soup)\n", "    \n", "    auction_status = soup.find('div', class_='ptsBoxOk mB20')\n", "    \n", "    if auction_status and 'すべての取引が完了しました' in auction_status.text:\n", "        auction_status = 'finished'\n", "    else:\n", "        auction_status = None\n", "        \n", "     \n", "\n", "    if delivery_method_raw:\n", "        if \"送料：出品者負担\" in delivery_method_raw:\n", "            shipping_fee = \"出品者負担\"\n", "        if \"着払い\" in delivery_method_raw:\n", "            shipping_fee = \"着払い\"\n", "            \n", "    # In kết quả\n", "    return {\n", "        \"payment_total\": payment_total,\n", "        \"shipping_fee\": shipping_fee,\n", "        \"status\": auction_status,\n", "        \"delivery_method\": delivery_method,\n", "        \"delivery_time\": delivery_time,\n", "        \"payment_method\": payment_method,\n", "        \"delivery_status\": delivery_status,\n", "        \"tracking_number\": tracking_number,\n", "    }\n"]}, {"cell_type": "code", "execution_count": 56, "id": "9e4bf84a1660b33e", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:53.362394Z", "start_time": "2025-02-17T23:31:27.791337Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["S<PERSON> lượng đấu giá sẽ xử lý:  4\n", "s1183667046 【ジャンク】 024AV スチール STHIL 中古 初爆OK チェーンソー 青森～ 送料無料 16500 2025-05-11T20:52:00 https://contact.auctions.yahoo.co.jp/buyer/top?aid=s1183667046&syid=cie_isi&bid=at_uit\n", "\n", "k1183891320 STIHL 021Cチェーンソー 10000 2025-05-10T20:24:00 https://contact.auctions.yahoo.co.jp/buyer/top?aid=k1183891320&syid=junjieyuanmiao2015&bid=at_uit&oid=75624270-5946876217-7178175\n", "\n", "u1183360922 エコー ECHO エンジンチェーンソー V4000 エンジンチェンソー 工具 切断機 木工 4850 2025-05-09T21:12:00 https://contact.auctions.yahoo.co.jp/buyer/top?aid=u1183360922&syid=kurogomakurogoma1&bid=at_uit&oid=76538065-7746792717-8861001\n", "\n", "x1182752480 25横/A067627-2/xx7000/L053-506/　STIHL スチール 42cc 024AVEQチェンソー 20004 2025-05-05T22:05:00 https://contact.auctions.yahoo.co.jp/buyer/top?aid=x1182752480&syid=bbuug53793&bid=at_uit&oid=78018832-6046450317-1392007\n", "\n"]}], "source": ["# Show as table with pandas\n", "\n", "from datetime import datetime\n", "try:\n", "    # Fix the is_null syntax - using 'is.null' instead of 'is_'\n", "    response = supabase.from_('auctions').select('*')\\\n", "        .eq('buy_from', 'auctions.yahoo.co.jp')\\\n", "        .neq('status', 'finished')\\\n", "        .is_('tracking_number', 'null')\\\n", "        .order('auction_end_time', desc=True)\\\n", "        .execute()\n", "    auctions = response.data\n", "except Exception as e:\n", "    print(f\"Error fetching auctions: {e}\")\n", "    \n", "    # Try alternative syntax if the first one fails\n", "    try:\n", "        response = supabase.from_('auctions').select('*')\\\n", "            .eq('buy_from', 'auctions.yahoo.co.jp')\\\n", "            .neq('status', 'finished')\\\n", "            .eq('tracking_number', None)\\\n", "            .order('auction_end_time', desc=True)\\\n", "            .execute()\n", "        auctions = response.data\n", "    except Exception as alt_e:\n", "        print(f\"Alternative query also failed: {alt_e}\")\n", "        auctions = []\n", "print(\"<PERSON><PERSON> lượng đấu giá sẽ xử lý: \", len(auctions))\n", "for auction in auctions:\n", "    print(auction['id'], auction['name'], auction['price'], auction['auction_end_time'], auction['contact_url'])\n", "    if auction['contact_url']:\n", "        detail = None\n", "        if \"buy.auc\" in auction['contact_url']:\n", "            detail = get_buy_detail(auction['contact_url'])\n", "        else:\n", "            detail = get_detail(auction['contact_url'])\n", "\n", "        if detail and \"delivery_date\" in detail.keys():\n", "            if detail[\"delivery_date\"] and \"年\" in detail[\"delivery_date\"]:\n", "                # convert to datetime\n", "                detail[\"delivery_date\"] = datetime.strptime(detail[\"delivery_date\"], '%Y年%m月%d日').isoformat()\n", "\n", "            else:\n", "                detail[\"delivery_date\"] = None\n", "        if detail and \"tracking_number\" in detail.keys() and detail[\"tracking_number\"]:\n", "            # remove all characters except numbers\n", "            detail[\"tracking_number\"] = ''.join(filter(str.isdigit, detail[\"tracking_number\"]))\n", "        if \"status\" not in detail.keys() or not detail[\"status\"]:\n", "            detail[\"status\"] = 'processing'\n", "        detail['buy_from'] = 'auctions.yahoo.co.jp'\n", "        ## check payment_total is int, if not, set to None\n", "        if detail['payment_total']:\n", "            try:\n", "                detail['payment_total'] = int(detail['payment_total'])\n", "            except:\n", "                detail['payment_total'] = None\n", "        supabase.from_('auctions').update(detail).eq('id', auction['id']).execute()"]}, {"cell_type": "code", "execution_count": 62, "id": "a5845ecd54950602", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:53.677732Z", "start_time": "2025-02-17T23:31:53.367309Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Số lượng cần review:  9\n"]}], "source": ["need_feedback = supabase.rpc('get_need_feedback',params={}).execute().data\n", "print(\"<PERSON><PERSON> l<PERSON> cần review: \", len(need_feedback))"]}, {"cell_type": "code", "execution_count": 63, "id": "2fed73e7afc0c397", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:53.790208Z", "start_time": "2025-02-17T23:31:53.778610Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n", "\n", "\n", "Đã review\n"]}], "source": ["\n", "from httpcore import TimeoutException\n", "from selenium.common import NoSuchElementException\n", "\n", "\n", "def get_url(contact_url):\n", "    try:\n", "        # Tìm nút mở rộng thông tin\n", "\n", "        expand_button = WebDriverWait(driver, 3).until(\n", "            EC.element_to_be_clickable((By.XPATH, \"//a[contains(text(),'まとめて取引を確認する')]\"))\n", "        )\n", "        if expand_button:\n", "            url = expand_button.get_attribute('href')\n", "            driver.get(url)\n", "            time.sleep(2)\n", "    except Exception:\n", "        print(f\"\")\n", "    try:\n", "        # Đ<PERSON><PERSON> tối đa 10 gi<PERSON>y cho phần tử xuất hiện và chứa văn bản\n", "        feedback_link = WebDriverWait(driver, 3).until(\n", "            EC.presence_of_element_located((By.XPATH, '//a[contains(text(),\"出品者を評価する\")]'))\n", "        )\n", "\n", "        # <PERSON><PERSON><PERSON> URL từ thuộc tính href\n", "        url = feedback_link.get_attribute(\"href\")\n", "        if url:\n", "            return url\n", "        print(\"URL:\", url)\n", "    except Exception as e:\n", "        # <PERSON><PERSON> như không có gì xảy ra nếu không tìm thấy phần tử\n", "        print(f\"\")\n", "\n", "    feedback_buttons = driver.find_elements(By.CSS_SELECTOR, 'a.libBtnBlueL')\n", "\n", "    if len(feedback_buttons) == 0:\n", "        try:\n", "            # Đ<PERSON>i cho checkbox xu<PERSON><PERSON> hiện\n", "            checkbox = WebDriverWait(driver, 3).until(\n", "                EC.presence_of_element_located((By.ID, \"jsCheckReceive\"))\n", "            )\n", "\n", "            # Đ<PERSON><PERSON> cho nút \"受け取り連絡\" hiển thị\n", "            button = WebDriverWait(driver, 3).until(\n", "                EC.presence_of_element_located((By.CSS_SELECTOR, 'input.libBtnRedL.jsOnReceiveButton'))\n", "            )\n", "\n", "            # <PERSON><PERSON><PERSON> tra nếu checkbox và nút có hiển thị\n", "            if checkbox.is_displayed():\n", "                # Bấm vào checkbox\n", "                checkbox.click()\n", "                # <PERSON><PERSON><PERSON><PERSON> nút \"受け取り連絡\"\n", "                time.sleep(1)\n", "                button.click()\n", "                time.sleep(3)\n", "                print(\"Checkbox và nút đã được bấm.\")\n", "                return get_url(contact_url)\n", "            else:\n", "                print(\"Không tìm thấy checkbox hoặc nút.\")\n", "        except (TimeoutException, NoSuchElementException):\n", "            print(\"Một trong các phần tử không tồn tại hoặc không hiển thị đúng.\")\n", "    else:\n", "        return feedback_buttons[0].get_attribute(\"href\")\n", "\n", "\n", "def get_feedback_url_by_xpath(xpath):\n", "    try:\n", "        feedback_link = WebDriverWait(driver, 3).until(\n", "            EC.presence_of_element_located((By.XPATH, xpath))\n", "        )\n", "        return feedback_link.get_attribute(\"href\") if feedback_link else None\n", "    except Exception as e:\n", "        print(f\"Failed to get feedback link by xpath: {xpath}\")\n", "\n", "\n", "def get_feedback_url_by_css(css):\n", "    try:\n", "        feedback_link = WebDriverWait(driver, 3).until(\n", "            EC.presence_of_element_located((By.CSS_SELECTOR, css))\n", "        )\n", "        return feedback_link.get_attribute(\"href\") if feedback_link else None\n", "    except Exception as e:\n", "        print(f\"Failed to get feedback link by css: {css}\")\n", "\n", "\n", "for auction in need_feedback:\n", "    driver.get(auction['contact_url'])\n", "    url = get_url(auction['contact_url'])\n", "    # wait and find button has text: \"出品者を評価する\"\n", "    url = url if url else get_feedback_url_by_xpath('//a[contains(text(),\"出品者を評価する\")]')\n", "    url = url if url else get_feedback_url_by_xpath(\n", "        '//a[@class=\"libBtnBlueL\" and contains(@href, \"auctions.yahoo.co.jp\") and contains(@href, \"出品者を評価する\")]')\n", "    url = url if url else get_feedback_url_by_css('a.libBtnBlueL')\n", "\n", "    if url:\n", "       \n", " \n", "        # Tìm và nhấn nút có code sau: <input id=\"commonTextIn\" type=\"button\" value=\"定型コメント入力\">\n", "        driver.get(url)\n", "        time.sleep(2)\n", "        try:\n", "            common_button = WebDriverWait(driver, 3).until(\n", "                EC.element_to_be_clickable((By.ID, \"commonTextIn\"))\n", "            )\n", "            if common_button:\n", "                common_button.click()\n", "                time.sleep(1)\n", "                # Tìm và nhấn nút có chữ ”確認する\"\n", "                confirm_button = WebDriverWait(driver, 3).until(\n", "                    EC.element_to_be_clickable((By.XPATH, \"//input[@value='確認する']\"))\n", "                )\n", "                if confirm_button:\n", "                    confirm_button.click()\n", "                    time.sleep(1)\n", "                    # Tìm và nhấn nút có chữ \"評価を公開する\"\n", "                    send_button = WebDriverWait(driver, 3).until(\n", "                        EC.element_to_be_clickable((By.XPATH, \"//input[@value='評価を公開する']\"))\n", "                    )\n", "                    if send_button:\n", "                        send_button.click()\n", "                        time.sleep(1)\n", "                        # <PERSON><PERSON><PERSON> nhật trạng thái đã review vào database\n", "                        supabase.table('auctions').update({'reviewed_at': \"now()\"}).eq('id',\n", "                                                                                       auction['id']).execute()\n", "                        print(\"Đã review\")\n", "        except Exception as e:\n", "            print(f\"Error: {e}\")\n", "    else:\n", "        driver.get(url)\n", "        time.sleep(2)\n", "        # Nếu ở đâu đó trong trang có text 前回、評価した内容 thì bỏ qua\n", "        if \"前回、評価した内容\" in driver.page_source:\n", "            supabase.table('auctions').update({'reviewed_at': \"now()\"}).eq('id',\n", "                                                                           auction['id']).execute()\n", "            print(\"Đã review rồi\")\n", "        "]}, {"cell_type": "markdown", "id": "bc91ecf4b49b6aa8", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> thông tin vận đơn từ tin nhắn (<PERSON><PERSON> dụng cho　着払い của ヤマト運輸)"]}, {"cell_type": "code", "execution_count": 67, "id": "4685d3c9fb3ea5db", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:53.917423Z", "start_time": "2025-02-17T23:31:53.796785Z"}}, "outputs": [{"ename": "APIError", "evalue": "{'code': 'PGRST100', 'details': 'unexpected \"o\" expecting null or trilean value (unknown, true, false)', 'hint': None, 'message': '\"failed to parse filter (is.None)\" (line 1, column 5)'}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[67]\u001b[39m\u001b[32m, line 42\u001b[39m\n\u001b[32m     34\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\"\u001b[39m * \u001b[32m50\u001b[39m)\n\u001b[32m     36\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m tracking_numbers  \u001b[38;5;66;03m# <PERSON><PERSON><PERSON> về danh s<PERSON>ch c<PERSON>c số tracking tìm đ<PERSON>\u001b[39;00m\n\u001b[32m     38\u001b[39m yamatos = (\u001b[43msupabase\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtable\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mauctions\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m*\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     39\u001b[39m \u001b[43m           \u001b[49m\u001b[43m.\u001b[49m\u001b[43meq\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mdelivery_method\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mヤマト運輸\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     40\u001b[39m \u001b[43m           \u001b[49m\u001b[43m.\u001b[49m\u001b[43mneq\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstatus\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mfinished\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m \u001b[43m           \u001b[49m\u001b[43m.\u001b[49m\u001b[43mis_\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mtracking_number\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m---> \u001b[39m\u001b[32m42\u001b[39m \u001b[43m           \u001b[49m\u001b[43m.\u001b[49m\u001b[43meq\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mshipping_fee\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m着払い\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m.data)\n\u001b[32m     44\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mSố lượng đơn hàng cần xử lý: \u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28mlen\u001b[39m(yamatos))\n\u001b[32m     45\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m yamato \u001b[38;5;129;01min\u001b[39;00m yamatos:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/workspace/fetch-yahoo-auctions/.venv/lib/python3.13/site-packages/postgrest/_sync/request_builder.py:70\u001b[39m, in \u001b[36mexecute\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     68\u001b[39m body = r.text\n\u001b[32m     69\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.headers.get(\u001b[33m\"\u001b[39m\u001b[33mAccept\u001b[39m\u001b[33m\"\u001b[39m) == \u001b[33m\"\u001b[39m\u001b[33mtext/csv\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m body\n\u001b[32m     71\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.headers.get(\n\u001b[32m     72\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mAccept\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     73\u001b[39m ) \u001b[38;5;129;01mand\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mapplication/vnd.pgrst.plan\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.headers.get(\u001b[33m\"\u001b[39m\u001b[33mAccept\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m     74\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33m+json\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.headers.get(\u001b[33m\"\u001b[39m\u001b[33mAccept\u001b[39m\u001b[33m\"\u001b[39m):\n", "\u001b[31mAPIError\u001b[39m: {'code': 'PGRST100', 'details': 'unexpected \"o\" expecting null or trilean value (unknown, true, false)', 'hint': None, 'message': '\"failed to parse filter (is.None)\" (line 1, column 5)'}"]}], "source": ["from bs4 import BeautifulSoup\n", "import re\n", "import time\n", "\n", "def get_tracking_number_from_message(contact_url):\n", "    # Sử dụng driver đ<PERSON> được khởi tạo sẵn\n", "    driver.get(contact_url)\n", "    time.sleep(1)  # <PERSON><PERSON><PERSON> trang tải xong\n", "    html_content = driver.page_source\n", "\n", "    # <PERSON><PERSON> tích cú pháp HTML\n", "    soup = BeautifulSoup(html_content, 'html.parser')\n", "\n", "    # T<PERSON><PERSON> tất cả các mục với class `ptsPartner`\n", "    partner_items = soup.find_all('dl', class_='ptsPartner')\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON> qua từng mục để trích xuất nội dung và tìm số tracking\n", "    tracking_numbers = []\n", "    for item in partner_items:\n", "        seller_id = item.find('p', id='sellerid').text.strip()  # L<PERSON>y ID người bán\n", "        date_time = item.find('span', class_='decTime').text.strip()  # <PERSON><PERSON>y thời gian\n", "        message_body = item.find('dd', id='body').text.strip()  # <PERSON><PERSON><PERSON> nội dung tin nhắn\n", "\n", "        # <PERSON><PERSON><PERSON> <PERSON> tracking trong tin nhắn bằng biểu thức ch<PERSON>h quy\n", "        tracking_number_match = re.search(r'\\d{4}-\\d{4}-\\d{4}', message_body)\n", "        if tracking_number_match:\n", "            tracking_number = tracking_number_match.group()\n", "            tracking_numbers.append(tracking_number)\n", "            print(\"Tracking Number:\", tracking_number)\n", "\n", "        print(\"Seller ID:\", seller_id)\n", "        print(\"Date and Time:\", date_time)\n", "        print(\"Message Body:\", message_body)\n", "        print(\"-\" * 50)\n", "\n", "    return tracking_numbers  # <PERSON><PERSON><PERSON> về danh sách các số tracking tìm đ<PERSON>\n", "\n", "yamatos = (supabase.table('auctions').select(\"*\")\n", "           .eq(\"delivery_method\",\"ヤマト運輸\")\n", "           .neq(\"status\",\"finished\")\n", "           .is_(\"tracking_number\", None)\n", "           .eq(\"shipping_fee\", \"着払い\").execute().data)\n", "\n", "print(\"<PERSON><PERSON> lượng đơn hàng cần xử lý: \", len(yamatos))\n", "for yamato in yamatos:\n", "    tracking_numbers = get_tracking_number_from_message(yamato['contact_url'])\n", "    # keep only number in tracking number, remove all characters except numbers\n", "    tracking_numbers = [''.join(filter(str.isdigit, tracking_number)) for tracking_number in tracking_numbers]\n", "    print(\"Tracking Numbers:\", tracking_numbers)\n", "    if tracking_numbers:\n", "        supabase.table('auctions').update({\"tracking_number\": tracking_numbers[0]}).eq(\"id\", yamato['id']).execute()"]}, {"cell_type": "code", "execution_count": 11, "id": "5052897de76be338", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:31:57.838927Z", "start_time": "2025-02-17T23:31:54.021287Z"}, "collapsed": false}, "outputs": [], "source": ["from selenium.webdriver.support.wait import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC  # Sử dụng đúng import cho EC\n", "\n", "yamatos = supabase.table('auctions').select(\"*\").eq(\"delivery_method\",\"ヤマト運輸\").neq(\"status\",\"finished\").execute().data\n", "tracking_numbers = [yamato['tracking_number'] for yamato in yamatos]\n", "# filter None\n", "tracking_numbers = [tracking_number for tracking_number in tracking_numbers if tracking_number]\n", "\n", "# Hàm để lấy thông tin trạng thái từ Yamato\n", "def get_tracking_status(tracking_numbers):\n", "    try:\n", "        # Mở trang tra cứu trạng thái của <PERSON>\n", "        driver.get(\"https://toi.kuronekoyamato.co.jp/cgi-bin/tneko\")  # Thay thế URL nếu cần\n", "\n", "        for i, number in enumerate(tracking_numbers):\n", "\n", "            input_box = driver.find_element(By.NAME, f\"number0{i+1}\")\n", "            input_box.clear()\n", "            input_box.send_keys(number)\n", "\n", "        # find submit button has name \"category\" and text \"お問い合わせ開始\"\n", "        # Đợi cho đến khi nút \"お問い合わせ開始\" có thể click được\n", "        inquiry_buttons = driver.find_elements(By.XPATH, \"//button[contains(text(), 'お問い合わせ開始')]\")\n", "        for button in inquiry_buttons:\n", "            try:\n", "                # Chỉ click vào nút có thể tương tác\n", "                WebDriverWait(driver, 2).until(EC.element_to_be_clickable(button))\n", "                button.click()\n", "                break\n", "            except:\n", "                continue\n", "\n", "\n", "        # <PERSON><PERSON><PERSON> một chút cho trang tải\n", "        time.sleep(1)\n", "\n", "        # Tìm phần tử chứa thông tin trạng thái\n", "        status_elements = driver.find_elements(By.CLASS_NAME, \"area-1\")  # Thay thế class name bằng class thật trong HTML\n", "        for element in status_elements:\n", "            try:\n", "                input = element.find_element(By.XPATH, \"div[@class='cell-1']//input[@type='text']\")\n", "                status = element.find_element(By.XPATH, \"div[@class='cell-2']\")\n", "                tracking_number_value = input.get_attribute(\"value\")\n", "                status_value = status.text\n", "                # keep tracking number value only digits\n", "                tracking_number_value = ''.join(filter(str.isdigit, tracking_number_value))\n", "                supabase.table('auctions').update({\"delivery_status\": status_value}).eq(\"tracking_number\", tracking_number_value).execute()\n", "            except:\n", "                return \"<PERSON><PERSON><PERSON><PERSON> tìm thấy giá trị của input.\"\n", "\n", "    except Exception as e:\n", "        print(f\"Lỗi: {e}\")\n", "\n", "# Tạo DataFrame để lưu trữ kết quả\n", "# max 8 tracking number\n", "tracking_number_chunks = [tracking_numbers[i:i+8] for i in range(0, len(tracking_numbers), 8)]\n", "\n", "for chunk in tracking_number_chunks:\n", "    get_tracking_status(chunk)\n", "# <PERSON><PERSON><PERSON> trình <PERSON>"]}, {"cell_type": "code", "execution_count": 65, "id": "bc123c930b1ccc99", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:32:00.539985Z", "start_time": "2025-02-17T23:31:57.898279Z"}, "collapsed": false}, "outputs": [], "source": ["from selenium.webdriver import Keys\n", "\n", "\n", "def get_tracking_status_sagawa(tracking_numbers):\n", "    try:\n", "        # Mở trang tra cứu trạng thái của <PERSON>\n", "        driver.get(\"https://k2k.sagawa-exp.co.jp/p/sagawa/web/okurijoinput.jsp\")  # Thay thế URL nếu cần\n", "\n", "        # Tìm và nhập số tracking vào ô input\n", "        for i, number in enumerate(tracking_numbers):\n", "            input_box = driver.find_element(By.NAME, f\"main:no{i+1}\")\n", "            input_box.clear()\n", "            input_box.send_keys(number)\n", "            if i == len(tracking_numbers) - 1:\n", "                input_box.send_keys(Keys.RETURN)\n", "\n", "        # Tìm và click vào nút tra cứu \"問い合わせ開始\"\n", "        # inquiry_button = WebDriverWait(driver, 10).until(\n", "        #     EC.element_to_be_clickable((By.XPATH, \"//input[@name='main:toiStart' and @value='お問い合わせ開始']\"))\n", "        # )\n", "        # inquiry_button.click()\n", "\n", "        # <PERSON><PERSON><PERSON> một chút cho trang tải\n", "        time.sleep(1)\n", "\n", "        # T<PERSON><PERSON> bảng chứa thông tin trạng thái\n", "        status_elements = driver.find_elements(By.CLASS_NAME, \"state\")\n", "        for element in status_elements:\n", "\n", "            # get parent element of state element\n", "            parent_element = element.find_element(By.XPATH, \"../..\")\n", "\n", "            if parent_element:\n", "                tracking_number = parent_element.text.split(\"\\n\")[1]\n", "                state_value = parent_element.text.split(\"\\n\")[2]\n", "                if tracking_number and state_value:\n", "                    # keep tracking number value only digits\n", "                    tracking_number_value = ''.join(filter(str.isdigit, tracking_number))\n", "                    supabase.table('auctions').update({\"delivery_status\": state_value}).eq(\"tracking_number\", tracking_number_value).execute()\n", "                    print(tracking_number, state_value)\n", "\n", "\n", "    except Exception as e:\n", "        return f\"Lỗi: {e}\"\n", "sagawas = supabase.table('auctions').select(\"*\").eq(\"delivery_method\",\"佐川急便\").neq(\"status\",\"finished\").execute().data\n", "tracking_numbers = [sagawa['tracking_number'] for sagawa in sagawas]\n", "# filter None\n", "tracking_numbers = [tracking_number for tracking_number in tracking_numbers if tracking_number]\n", "tracking_number_chunks = [tracking_numbers[i:i+8] for i in range(0, len(tracking_numbers), 8)]\n", "for chunk in tracking_number_chunks:\n", "    get_tracking_status_sagawa(chunk)\n"]}, {"cell_type": "code", "execution_count": 69, "id": "8e17597440f1bb8e", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:32:01.172153Z", "start_time": "2025-02-17T23:32:00.615848Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tổng số tiền: 0円\n"]}], "source": ["total = 0\n", "tracking_numbers = []\n", "\n", "plan_ids = [1,2,5]\n", "# <PERSON><PERSON><PERSON> dữ liệu từ bảng 'auctions'\n", "all_auc = supabase.from_('auctions').select().in_('plan_id', plan_ids).execute().data\n", "additional_expenses = supabase.from_('additional_expenses').select().in_('plan_id', plan_ids).execute().data\n", "\n", "# Lặp qua tất cả các dòng dữ liệu\n", "for auc in all_auc:\n", "    # Kiểm tra nếu payment_total có giá trị\n", "    if auc['payment_total']:\n", "        # Nếu tracking_number có giá trị (kh<PERSON>ng null hoặc trống), loại bỏ trùng nhau\n", "        if auc['tracking_number']:\n", "            if auc['tracking_number'] not in tracking_numbers:\n", "                total += auc['payment_total']\n", "                tracking_numbers.append(auc['tracking_number'])\n", "        else:\n", "            # Nếu tracking_number là null hoặc trống, vẫn tính vào tổng\n", "            total += auc['payment_total']\n", "        # <PERSON><PERSON><PERSON> có trả trư<PERSON>, cộng vào tổng\n", "    else:\n", "        total += auc['price']\n", "        print(\"Taạm tính: \", auc['price'])\n", "    total+= auc['cod']\n", "\n", "total += sum([expense['amount'] for expense in additional_expenses])\n", "# In ra tổng số tiền\n", "print(f\"Tổng số tiền: {total}円\")\n", "# In ra danh sách tracking_numbers để kiểm tra"]}, {"cell_type": "code", "execution_count": 66, "id": "e8081a0e1dbacd66", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:32:01.514047Z", "start_time": "2025-02-17T23:32:01.203222Z"}}, "outputs": [{"ename": "NameError", "evalue": "name 'total' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[66]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m exchanges = supabase.from_(\u001b[33m'\u001b[39m\u001b[33mexchanges\u001b[39m\u001b[33m'\u001b[39m).select(\u001b[33m'\u001b[39m\u001b[33mjpy, vnd\u001b[39m\u001b[33m'\u001b[39m).execute().data\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# <PERSON><PERSON>i muôn print daạng x.xxxx (ddau phay 4 chu so)\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m balance_jpy = \u001b[38;5;28msum\u001b[39m([exchange[\u001b[33m'\u001b[39m\u001b[33mjpy\u001b[39m\u001b[33m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m exchange \u001b[38;5;129;01min\u001b[39;00m exchanges]) - \u001b[43mtotal\u001b[49m\n\u001b[32m      5\u001b[39m balance_vnd = \u001b[38;5;28msum\u001b[39m([exchange[\u001b[33m'\u001b[39m\u001b[33mvnd\u001b[39m\u001b[33m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m exchange \u001b[38;5;129;01min\u001b[39;00m exchanges])\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mformat_japanese_currency\u001b[39m(amount):\n\u001b[32m      7\u001b[39m     \u001b[38;5;66;03m# Lấy số hàng vạn\u001b[39;00m\n", "\u001b[31mNameError\u001b[39m: name 'total' is not defined"]}], "source": ["exchanges = supabase.from_('exchanges').select('jpy, vnd').execute().data\n", "\n", "# Toôi muôn print daạng x.xxxx (ddau phay 4 chu so)\n", "balance_jpy = sum([exchange['jpy'] for exchange in exchanges]) - total\n", "balance_vnd = sum([exchange['vnd'] for exchange in exchanges])\n", "def format_japanese_currency(amount):\n", "    # <PERSON><PERSON><PERSON> số hàng vạn\n", "    man = amount // 10000\n", "    # <PERSON><PERSON><PERSON> ph<PERSON>n dư\n", "    remainder = amount % 10000\n", "\n", "    # <PERSON><PERSON><PERSON> phần dư là 0 thì chỉ in \"万\"\n", "    if remainder == 0:\n", "        return f\"{man}万\"\n", "    else:\n", "        return f\"<PERSON>ố dư:  {man} 万 {remainder}円\"\n", "def format_vietnamese_currency(amount):\n", "    return f\"{amount:,.0f} VND\"\n", "print(format_japanese_currency(balance_jpy))\n", "print(format_vietnamese_currency(balance_vnd))\n"]}, {"cell_type": "code", "execution_count": null, "id": "d4c8aed8856a2068", "metadata": {"ExecuteTime": {"end_time": "2025-02-17T23:32:01.572633Z", "start_time": "2025-02-17T23:32:01.569976Z"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}