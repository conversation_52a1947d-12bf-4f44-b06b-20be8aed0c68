import argparse
import time
from supabase import create_client
import google.generativeai as genai

import config
from utils.selenium_utils import initialize_driver, login_to_yahoo
from services.yahoo_auction_service import YahooAuctionService
from services.tracking_service import TrackingService

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Yahoo Auction Service')
    parser.add_argument('--fetch', action='store_true', help='Fetch won auctions')
    parser.add_argument('--process', action='store_true', help='Process pending auctions')
    parser.add_argument('--feedback', action='store_true', help='Process feedback')
    parser.add_argument('--track', action='store_true', help='Update tracking information')
    parser.add_argument('--all', action='store_true', help='Run all operations')

    args = parser.parse_args()

    # Initialize Supabase client
    supabase = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)

    # Initialize Gemini client
    genai.configure(api_key=config.GEMINI_API_KEY)
    genai_client = genai

    # Initialize WebDriver
    driver = initialize_driver()

    try:
        # Login to Yahoo Auctions
        if not login_to_yahoo(driver, auto_login=True):
            print("Login failed. Please check your credentials.")
            return

        # Initialize services
        yahoo_service = YahooAuctionService(driver, supabase, genai_client)
        tracking_service = TrackingService(driver, supabase)

        # Run operations based on arguments
        if args.fetch or args.all:
            print("Fetching won auctions...")
            auctions = yahoo_service.fetch_won_auctions()
            yahoo_service.process_auction_data(auctions)

        if args.process or args.all:
            print("Processing pending auctions...")
            yahoo_service.process_pending_auctions()

        if args.feedback or args.all:
            print("Processing feedback...")
            yahoo_service.process_feedback()

        if args.track or args.all:
            print("Updating tracking information...")
            tracking_service.update_all_tracking()

        # If no specific operation is requested, show help
        if not (args.fetch or args.process or args.feedback or args.track or args.all):
            parser.print_help()

    finally:
        # Close the WebDriver
        print("Closing WebDriver...")
        driver.quit()

if __name__ == "__main__":
    main()
