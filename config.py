import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://cduauvwekzwtsmwgsngf.supabase.co")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNkdWF1dndla3p3dHNtd2dzbmdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjkzMDk0MjYsImV4cCI6MjA0NDg4NTQyNn0.V6pQ0FRD69bYQ-CgoiKPH9SXNqdXSs-9QYM_UtqjNVw")

# Google Gemini API configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCoBwVOiPHG-CwA-TdKbtPAPoxHFkXKAok")

# Yahoo Auction credentials
YAHOO_USERNAME = os.getenv("YAHOO_USERNAME", "08063896022")
YAHOO_PASSWORD = os.getenv("YAHOO_PASSWORD", "")

# URLs
YAHOO_AUCTION_URL = "https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won"

# Selenium configuration
CHROME_OPTIONS = {
    "incognito": True,
    "no_sandbox": True,
    "disable_dev_shm_usage": True,
    "verbose": True
}
