from datetime import datetime
import json
import os
import requests
import time
from typing import Dict, List, Any

def custom_encoder(obj):
    """Convert datetime objects to ISO 8601 format for JSON serialization."""
    if isinstance(obj, datetime):
        return obj.isoformat()  # ISO 8601 format (YYYY-MM-DDTHH:MM:SS)
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

def clean_price(price_str):
    """Clean price string and convert to integer."""
    if not price_str:
        return 0

    if "円" in price_str:
        return int(price_str.replace(",", "").replace("円", "").replace(" ", ""))
    return 0

def parse_auction_end_time(end_time_str):
    """Parse auction end time string to datetime object."""
    if not end_time_str:
        return None

    # Remove newlines if present
    if "\n" in end_time_str:
        end_time_str = end_time_str.split("\n")[0]

    try:
        # Parse date in format '月日 時分'
        end_time = datetime.strptime(end_time_str, '%m月%d日 %H時%M分')
        # Set current year
        end_time = end_time.replace(year=datetime.now().year)
        return end_time
    except ValueError as e:
        print(f"Error parsing date: {end_time_str} - {e}")
        return None

def clean_tracking_number(tracking_number):
    """Clean tracking number to contain only digits."""
    if not tracking_number:
        return None
    return ''.join(filter(str.isdigit, tracking_number))

def format_japanese_currency(amount):
    """Format amount in Japanese currency style (万円)."""
    # Get number of 10,000s
    man = amount // 10000
    # Get remainder
    remainder = amount % 10000

    # If remainder is 0, just print "万"
    if remainder == 0:
        return f"{man}万"
    else:
        return f"{man}万{remainder}円"

def format_vietnamese_currency(amount):
    """Format amount in Vietnamese currency style."""
    return f"{amount:,.0f} VND"

def identify_delivery_company(delivery_method_raw):
    """Identify the delivery company from raw text."""
    if not delivery_method_raw:
        return None

    if "ヤマト" in delivery_method_raw or "おてがる配送" in delivery_method_raw:
        return "ヤマト運輸"
    elif "佐川" in delivery_method_raw:
        return "佐川急便"
    elif "カンガルー" in delivery_method_raw or "西濃" in delivery_method_raw:
        return "西濃運輸"
    elif "ゆうパック" in delivery_method_raw:
        return "ゆうパック"
    elif "福山" in delivery_method_raw:
        return "福山通運"

    return delivery_method_raw

def extract_cookies(driver) -> List[Dict[str, Any]]:
    """Extract cookies from the current browser session."""
    if not driver:
        return []
    return driver.get_cookies()

def save_cookies(cookies: List[Dict[str, Any]], filename: str = 'data/yahoo_cookies.json') -> bool:
    """Save cookies to a file.

    Args:
        cookies: List of cookie dictionaries
        filename: Path to save the cookies

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(cookies, f, indent=4)
        return True
    except Exception as e:
        print(f"Error saving cookies: {e}")
        return False

def load_cookies(filename: str = 'data/yahoo_cookies.json') -> List[Dict[str, Any]]:
    """Load cookies from a file.

    Args:
        filename: Path to the cookie file

    Returns:
        List of cookie dictionaries or empty list if file doesn't exist
    """
    if not os.path.exists(filename):
        return []

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading cookies: {e}")
        return []

def add_cookies_to_driver(driver, cookies: List[Dict[str, Any]]) -> bool:
    """Add cookies to a WebDriver session.

    Args:
        driver: Selenium WebDriver instance
        cookies: List of cookie dictionaries

    Returns:
        bool: True if successful, False otherwise
    """
    if not driver or not cookies:
        print("No driver or cookies provided")
        return False

    try:
        # Make sure we're on the right domain before adding cookies
        current_url = driver.current_url
        if 'yahoo.co.jp' not in current_url:
            print("Navigating to Yahoo domain before adding cookies...")
            driver.get('https://www.yahoo.co.jp/')
            time.sleep(3)  # Give more time for page to load

        # Group cookies by domain
        domain_cookies = {}
        for cookie in cookies:
            domain = cookie.get('domain', '.yahoo.co.jp')
            if domain not in domain_cookies:
                domain_cookies[domain] = []
            domain_cookies[domain].append(cookie)

        print(f"Grouped cookies by {len(domain_cookies)} domains")

        # Add cookies for each domain
        success_count = 0
        total_cookies = len(cookies)

        # First add the main Yahoo domain cookies
        main_domains = ['.yahoo.co.jp', 'yahoo.co.jp', 'www.yahoo.co.jp']
        for domain in main_domains:
            if domain in domain_cookies:
                domain_cookies_list = domain_cookies[domain]
                print(f"Adding {len(domain_cookies_list)} cookies for domain {domain}")

                # Make sure we're on the right domain
                driver.get('https://www.yahoo.co.jp/')
                time.sleep(2)  # Give time for page to load

                # Add cookies for this domain
                for cookie in domain_cookies_list:
                    # Some cookie attributes might cause issues when adding to driver
                    # Only keep the essential attributes
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain'),
                        'path': cookie.get('path', '/'),
                    }

                    # Remove domain if it's None
                    if clean_cookie['domain'] is None:
                        del clean_cookie['domain']

                    # Add expiry if it exists
                    if 'expiry' in cookie:
                        clean_cookie['expiry'] = cookie['expiry']

                    try:
                        driver.add_cookie(clean_cookie)
                        success_count += 1
                    except Exception as e:
                        print(f"Error adding cookie {cookie['name']}: {e}")

                # Remove this domain from the dictionary to avoid processing it again
                del domain_cookies[domain]

        # Now add cookies for other domains
        for domain, domain_cookies_list in domain_cookies.items():
            print(f"Adding {len(domain_cookies_list)} cookies for domain {domain}")

            # Visit domain if needed
            try:
                if domain.startswith('.'):
                    # For .yahoo.co.jp, we can use www.yahoo.co.jp
                    base_domain = domain[1:] if domain.startswith('.') else domain
                    driver.get(f"https://www.{base_domain}/")
                else:
                    # For specific subdomains
                    driver.get(f"https://{domain}/")

                time.sleep(2)  # Give more time for page to load
            except Exception as e:
                print(f"Error navigating to domain {domain}: {e}")
                continue

            # Add cookies for this domain
            for cookie in domain_cookies_list:
                # Some cookie attributes might cause issues when adding to driver
                # Only keep the essential attributes
                clean_cookie = {
                    'name': cookie['name'],
                    'value': cookie['value'],
                    'domain': cookie.get('domain'),
                    'path': cookie.get('path', '/'),
                }

                # Remove domain if it's None
                if clean_cookie['domain'] is None:
                    del clean_cookie['domain']

                # Add expiry if it exists
                if 'expiry' in cookie:
                    clean_cookie['expiry'] = cookie['expiry']

                try:
                    driver.add_cookie(clean_cookie)
                    success_count += 1
                except Exception as e:
                    print(f"Error adding cookie {cookie['name']}: {e}")

        print(f"Successfully added {success_count} out of {total_cookies} cookies")

        # Navigate to Yahoo Auctions to verify cookies
        print("Navigating to Yahoo Auctions to verify cookies...")
        driver.get('https://auctions.yahoo.co.jp/')
        time.sleep(3)

        # Không cần chụp ảnh màn hình nữa
        print(f"Added {success_count} cookies to driver")

        return success_count > 0
    except Exception as e:
        print(f"Error adding cookies to driver: {e}")
        return False

def check_cookie_status(cookies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Check if cookies are valid by making a test request.

    Args:
        cookies: List of cookie dictionaries

    Returns:
        Dict with status information
    """
    if not cookies:
        return {'valid': False, 'message': 'No cookies provided'}

    try:
        # Convert cookies to requests format
        cookie_jar = {}
        for cookie in cookies:
            cookie_jar[cookie['name']] = cookie['value']

        # Make a request to Yahoo Auctions won items page
        url = 'https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won'
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
        response = requests.get(url, cookies=cookie_jar, headers=headers, allow_redirects=False)

        # First check if we were redirected (status code 302)
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', '')
            if 'login' in redirect_url or 'auth' in redirect_url:
                return {
                    'valid': False,
                    'message': 'Cookies are invalid (redirected to login page)',
                    'status_code': response.status_code,
                    'redirect_url': redirect_url
                }

        # If we got a 200 response, check the content
        if response.status_code == 200:
            # Check if we're logged in by looking for specific elements in the response
            login_indicators = ['ログイン', 'login', 'sign in', 'yahoo! japan id']
            logged_in_indicators = ['ログアウト', 'logout', '落札物', '落札物一覧']

            # Check for login indicators (not logged in)
            if any(indicator.lower() in response.text.lower() for indicator in login_indicators):
                # Double check if we also have logged-in indicators
                if any(indicator.lower() in response.text.lower() for indicator in logged_in_indicators):
                    return {
                        'valid': True,
                        'message': 'Cookies appear valid (mixed signals)',
                        'status_code': response.status_code
                    }
                else:
                    return {
                        'valid': False,
                        'message': 'Cookies are invalid or expired',
                        'status_code': response.status_code
                    }
            else:
                # Check for logged-in indicators
                if any(indicator.lower() in response.text.lower() for indicator in logged_in_indicators):
                    return {
                        'valid': True,
                        'message': 'Cookies are valid',
                        'status_code': response.status_code
                    }
                else:
                    return {
                        'valid': False,
                        'message': 'Cookies status unclear (no clear indicators)',
                        'status_code': response.status_code
                    }
        else:
            # Unexpected status code
            return {
                'valid': False,
                'message': f'Unexpected status code: {response.status_code}',
                'status_code': response.status_code
            }
    except Exception as e:
        return {'valid': False, 'message': f'Error checking cookies: {str(e)}'}
