from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import os
import config
from utils.data_utils import load_cookies, add_cookies_to_driver

def initialize_driver(use_cookies=True, cookies_file='data/yahoo_cookies.json', headless=False):
    """Initialize and return a Chrome WebDriver with configured options.

    Args:
        use_cookies: Whether to try loading cookies
        cookies_file: Path to the cookies file
        headless: Whether to run in headless mode

    Returns:
        WebDriver instance
    """
    options = webdriver.ChromeOptions()

    # Add options from config
    if config.CHROME_OPTIONS.get("incognito") and not use_cookies:
        # Don't use incognito mode if we're using cookies
        options.add_argument("--incognito")
    if config.CHROME_OPTIONS.get("no_sandbox"):
        options.add_argument("--no-sandbox")
    if config.CHROME_OPTIONS.get("disable_dev_shm_usage"):
        options.add_argument("--disable-dev-shm-usage")
    if config.CHROME_OPTIONS.get("verbose"):
        options.add_argument("--verbose")
    
    # Add headless mode if requested
    if headless:
        options.add_argument("--headless=new")
        options.add_argument("--window-size=1920,1080")

    # Initialize Chrome service and driver
    service = Service()
    driver = webdriver.Chrome(service=service, options=options)

    # Try to load cookies if requested
    if use_cookies and os.path.exists(cookies_file):
        # Load cookies and add them to the driver
        cookies = load_cookies(cookies_file)
        if cookies:
            print(f"Loaded {len(cookies)} cookies from {cookies_file}")
            # Print some cookie details for debugging
            auth_cookies = [c for c in cookies if 'auth' in c.get('name', '').lower() or 'login' in c.get('name', '').lower() or 'sess' in c.get('name', '').lower()]
            if auth_cookies:
                print(f"Found {len(auth_cookies)} authentication-related cookies")
                for cookie in auth_cookies[:3]:  # Show first 3 auth cookies
                    expiry = cookie.get('expiry', 'unknown')
                    if isinstance(expiry, (int, float)):
                        import datetime
                        expiry_date = datetime.datetime.fromtimestamp(expiry).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"Cookie '{cookie['name']}' expires on {expiry_date}")
            add_cookies_to_driver(driver, cookies)

    return driver

def login_to_yahoo(driver, check_cookies_first=True, auto_login=True):
    """Login to Yahoo Auctions.

    Args:
        driver: WebDriver instance
        check_cookies_first: Whether to check if we're already logged in first
        auto_login: Whether to automatically login if cookies are invalid

    Returns:
        bool: True if login successful, False otherwise
    """
    # Navigate to Yahoo Auctions
    driver.get(config.YAHOO_AUCTION_URL)
    time.sleep(5)  # Give more time for page to load and cookies to apply

    # Check if we're already logged in
    if check_cookies_first:
        try:
            # First check the URL - if we were redirected to login page, cookies are invalid
            current_url = driver.current_url
            print(f"Current URL: {current_url}")

            if "login" in current_url or "auth" in current_url:
                print("Redirected to login page, cookies are invalid")
                if not auto_login:
                    return False
                # If auto_login is True, we'll continue to the login process below
                print("Auto-login enabled, proceeding to login...")
            else:
                # Check for elements that indicate we're logged in
                # For example, check if login form is not present
                login_elements = driver.find_elements(By.ID, "login_handle")
                print(f"Login form elements found: {len(login_elements)}")

                if not login_elements:
                    # Check for a common element that appears when logged in
                    username_elements = driver.find_elements(By.CLASS_NAME, "LoginMenu__name")
                    logout_text = "ログアウト" in driver.page_source
                    auction_elements = driver.find_elements(By.XPATH, "//a[contains(@href, 'auctions.yahoo.co.jp')]")
                    mypage_elements = driver.find_elements(By.XPATH, "//a[contains(@href, 'mystatus')]")

                    print(f"Username elements: {len(username_elements)}, Logout text: {logout_text}")
                    print(f"Auction elements: {len(auction_elements)}, MyPage elements: {len(mypage_elements)}")

                    if username_elements or logout_text or (auction_elements and mypage_elements):
                        print("Already logged in via cookies")
                        return True
                    else:
                        print("No login form, but also no clear indication of being logged in")
                        print("チェック完了: ログイン状態を確認しました")

                        # If we're not sure if we're logged in, but auto_login is enabled,
                        # we'll try to navigate to a page that requires login
                        if auto_login:
                            driver.get("https://auctions.yahoo.co.jp/closeduser/jp/show/mystatus?select=won")
                            time.sleep(3)

                            # Check if we were redirected to login page
                            current_url = driver.current_url
                            if "login" in current_url or "auth" in current_url:
                                print("Redirected to login page after navigation, cookies are invalid")
                                # Continue to login process below
                            else:
                                # We're probably logged in
                                return True
                else:
                    print("Login form detected, not logged in")
                    if not auto_login:
                        return False
                    # If auto_login is True, we'll continue to the login process below
                    print("Auto-login enabled, proceeding to login...")
        except Exception as e:
            print(f"Error checking login status: {e}")
            if not auto_login:
                return False
            # If auto_login is True, we'll continue to the login process below
            print("Auto-login enabled, proceeding to login despite error...")

    # If we're not logged in, proceed with manual login
    try:
        # Find login field and enter username
        login_field = WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.ID, "login_handle"))
        )
        login_field.send_keys(config.YAHOO_USERNAME)
        print(f"Entered username: {config.YAHOO_USERNAME}")

        # Click next button
        next_button = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, "//button[text()='次へ']"))
        )
        next_button.click()
        print("Clicked next button")

        # Wait for page to load
        time.sleep(3)

        # Start authentication
        try:
            auth_button = WebDriverWait(driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//button[text()='認証を開始']"))
            )
            auth_button.click()
            print("Clicked authentication button")
        except Exception as auth_e:
            print(f"Authentication button not found or not clickable: {auth_e}")
            print("Trying alternative authentication methods...")

            # Try to find any other authentication buttons
            try:
                # Look for any button that might be related to authentication
                buttons = driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    button_text = button.text.strip()
                    print(f"Found button with text: '{button_text}'")
                    if "認証" in button_text or "確認" in button_text or "ログイン" in button_text:
                        print(f"Clicking button with text: '{button_text}'")
                        button.click()
                        break
            except Exception as e:
                print(f"Failed to find alternative authentication buttons: {e}")

        # Wait for manual authentication with a longer timeout
        print("Please complete the authentication manually...")
        print("You have 60 seconds to complete the authentication process.")
        print("Please enter the verification code and complete all steps.")
        time.sleep(60)

        # 認証後の状態を確認
        print("認証処理後の状態を確認しています")

        # Check if we're logged in after authentication
        login_elements = driver.find_elements(By.ID, "login_handle")
        if not login_elements and ("ログアウト" in driver.page_source):
            print("Successfully logged in!")
            return True
        else:
            print("Login may have failed. Please check the screenshot.")
            return False
    except Exception as e:
        print(f"Login failed: {e}")
        # ログイン失敗ログ
        print("ログインに失敗しました")
        return False

def wait_and_click(driver, by, value, timeout=10):
    """Wait for an element to be clickable and then click it."""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
        element.click()
        return True
    except Exception as e:
        print(f"Failed to click element {value}: {e}")
        return False

def wait_for_element(driver, by, value, timeout=10):
    """Wait for an element to be present and return it."""
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        return element
    except Exception as e:
        print(f"Element {value} not found: {e}")
        return None

def safe_get_attribute(element, attribute):
    """Safely get an attribute from an element."""
    try:
        return element.get_attribute(attribute)
    except:
        return None
